/** @odoo-module **/

import { _t } from "@web/core/l10n/translation";
import { user } from "@web/core/user";
import { useService } from "@web/core/utils/hooks";

import { Component } from "@odoo/owl";

export class SendWhatsAppButton extends Component {
    static template = "whatsapp.SendWhatsAppButton";
    static props = ["*"];

    setup() {
        this.action = useService("action");
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.title = _t("Send WhatsApp Message");
    }

    get phoneHref() {
        return "whatsapp://send?phone=" + this.props.record.data[this.props.name].replace(/\s+/g, "");
    }

    async onClick() {
        // Check if WhatsApp is available before opening composer
        try {
            const hasWhatsAppAccess = await this.orm.call(
                "whatsapp.template",
                "_can_use_whatsapp",
                [this.props.record.resModel]
            );

            if (!hasWhatsAppAccess) {
                this.notification.add(
                    _t("WhatsApp is not available. Please check your WhatsApp account configuration or contact your administrator."),
                    { type: "warning" }
                );
                return;
            }

            await this.props.record.save();
            this.action.doAction(
                {
                    type: "ir.actions.act_window",
                    target: "new",
                    name: this.title,
                    res_model: "whatsapp.composer",
                    views: [[false, "form"]],
                    context: {
                        ...user.context,
                        active_model: this.props.record.resModel,
                        active_id: this.props.record.resId,
                        default_phone: this.props.record.data[this.props.name],
                    },
                },
                {
                    onClose: () => {
                        this.props.record.load();
                        this.props.record.model.notify();
                    },
                }
            );
        } catch (error) {
            this.notification.add(
                _t("Error checking WhatsApp availability: %s", error.message || error),
                { type: "danger" }
            );
        }
    }
}
