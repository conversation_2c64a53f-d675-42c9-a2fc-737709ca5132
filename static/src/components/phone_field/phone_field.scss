/* WhatsApp Phone Field Button Styling */

.o_field_phone_whatsapp {
    color: #25D366 !important; /* WhatsApp green color */
    text-decoration: none;
    transition: all 0.2s ease;
    
    &:hover {
        color: #128C7E !important; /* Darker WhatsApp green on hover */
        text-decoration: none;
    }
    
    &:focus {
        color: #128C7E !important;
        text-decoration: none;
        outline: none;
    }
    
    i.fa-whatsapp {
        color: inherit;
        font-size: 1.1em;
    }
    
    small {
        color: inherit;
        font-weight: 600;
    }
}

/* Ensure consistent spacing with SMS button */
.o_phone_content {
    .o_field_phone_sms + .o_field_phone_whatsapp {
        margin-left: 0.5rem; /* Consistent spacing between SMS and WhatsApp buttons */
    }
}

/* Hide buttons on non-touch devices when not hovering */
body:not(.o_touch_device) .o_field_phone {
    &:not(:hover):not(:focus-within) {
        .o_field_phone_whatsapp {
            display: none !important;
        }
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .o_field_phone_whatsapp {
        small {
            display: none; /* Hide text on small screens, show only icon */
        }
        
        i.fa-whatsapp {
            font-size: 1.2em;
        }
    }
}

/* Loading state for WhatsApp availability check */
.o_field_phone_whatsapp_loading {
    opacity: 0.5;
    pointer-events: none;
}

/* Disabled state when WhatsApp is not available */
.o_field_phone_whatsapp_disabled {
    opacity: 0.3;
    pointer-events: none;
    cursor: not-allowed;
}
