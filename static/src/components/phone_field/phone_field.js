/** @odoo-module **/

import { _t } from "@web/core/l10n/translation";
import { patch } from "@web/core/utils/patch";
import { PhoneField, phoneField, formPhoneField } from "@web/views/fields/phone/phone_field";
import { SendWhatsAppButton } from "../whatsapp_button/whatsapp_button.js";
import { useService } from "@web/core/utils/hooks";
import { useState, onWillStart } from "@odoo/owl";

// Global cache for WhatsApp availability to reduce API calls
const whatsappAvailabilityCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

patch(PhoneField, {
    components: {
        ...PhoneField.components,
        SendWhatsAppButton,
    },
    defaultProps: {
        ...PhoneField.defaultProps,
        enableWhatsAppButton: true,
    },
    props: {
        ...PhoneField.props,
        enableWhatsAppButton: { type: Boolean, optional: true },
    },
});

// Add WhatsApp availability check to Phone<PERSON>ield
patch(PhoneField.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.whatsappState = useState({
            isAvailable: false,
            isLoading: true,
        });

        onWillStart(async () => {
            await this.checkWhatsAppAvailability();
        });
    },

    async checkWhatsAppAvailability() {
        if (!this.props.enableWhatsAppButton) {
            this.whatsappState.isAvailable = false;
            this.whatsappState.isLoading = false;
            return;
        }

        try {
            const hasAccess = await this.orm.call(
                "whatsapp.template",
                "_can_use_whatsapp",
                [this.props.record.resModel]
            );
            this.whatsappState.isAvailable = hasAccess;
        } catch (error) {
            console.warn("WhatsApp availability check failed:", error);
            this.whatsappState.isAvailable = false;
        } finally {
            this.whatsappState.isLoading = false;
        }
    },

    get showWhatsAppButton() {
        return (
            this.props.enableWhatsAppButton &&
            !this.whatsappState.isLoading &&
            this.whatsappState.isAvailable &&
            this.props.record.data[this.props.name] &&
            this.props.record.data[this.props.name].length > 0
        );
    },
});

const patchDescr = {
    extractProps({ options }) {
        const props = super.extractProps(...arguments);
        props.enableWhatsAppButton = options.enable_whatsapp !== false; // Default to true unless explicitly disabled
        return props;
    },
    supportedOptions: [
        ...(phoneField.supportedOptions ? phoneField.supportedOptions : []),
        {
            label: _t("Enable WhatsApp"),
            name: "enable_whatsapp",
            type: "boolean",
            default: true,
        },
    ],
};

patch(phoneField, patchDescr);
patch(formPhoneField, patchDescr);
