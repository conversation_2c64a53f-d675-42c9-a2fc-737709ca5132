/* @odoo-module */

import { Composer } from "@mail/core/common/composer";
import { patch } from "@web/core/utils/patch";
import { _t } from "@web/core/l10n/translation";

patch(Composer.prototype, {
    setup() {
        super.setup(...arguments);
    },

    /**
     * Check if WhatsApp send option should be available
     */
    get canSendWhatsApp() {
        // Show WhatsApp option for:
        // 1. Partner threads (contacts)
        // 2. Regular discuss channels (not WhatsApp channels)
        // 3. Any thread that has a model and ID
        if (!this.props.thread) {
            return false;
        }

        // Don't show for existing WhatsApp channels
        if (this.props.thread.channel_type === "whatsapp") {
            return false;
        }

        // Show for partner threads
        if (this.props.thread.model === 'res.partner' && this.props.thread.id) {
            return true;
        }

        // Show for other record types that might have contacts
        if (this.props.thread.model && this.props.thread.id &&
            this.props.thread.model !== 'discuss.channel') {
            return true;
        }

        return false;
    },

    /**
     * Send message via WhatsApp
     */
    async sendViaWhatsApp() {
        if (!this.state.text.trim()) {
            this.env.services.notification.add(
                _t("Please enter a message to send via WhatsApp"),
                { type: "warning" }
            );
            return;
        }

        const messageBody = this.state.text.trim();

        // Determine context based on thread type
        let context = {
            default_message_body: messageBody,
        };

        if (this.props.thread.model && this.props.thread.id) {
            context.default_thread_model = this.props.thread.model;
            context.default_thread_id = this.props.thread.id;

            // For partner threads, set partner_id directly
            if (this.props.thread.model === 'res.partner') {
                context.default_partner_id = this.props.thread.id;
            }
        }

        try {
            // Open WhatsApp composer dialog
            await this.env.services.action.doAction({
                type: "ir.actions.act_window",
                name: _t("Send WhatsApp Message"),
                res_model: "whatsapp.direct.message",
                view_mode: "form",
                views: [[false, "form"]],
                target: "new",
                context: context,
            });

            // Clear the composer after opening WhatsApp dialog
            this.state.text = "";

        } catch (error) {
            console.error("Error opening WhatsApp composer:", error);
            this.env.services.notification.add(
                _t("Failed to open WhatsApp composer"),
                { type: "danger" }
            );
        }
    },

    /**
     * Handle keyboard shortcuts
     */
    onKeydown(ev) {
        // Ctrl+Shift+W to send via WhatsApp
        if (ev.ctrlKey && ev.shiftKey && ev.key === 'W' && this.canSendWhatsApp) {
            ev.preventDefault();
            this.sendViaWhatsApp();
            return;
        }

        return super.onKeydown(ev);
    },
});
