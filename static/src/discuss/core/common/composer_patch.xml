<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="whatsapp.Composer" t-inherit="mail.Composer" t-inherit-mode="extension">
        <!-- Add WhatsApp button to composer toolbar -->
        <xpath expr="//div[hasclass('o-mail-Composer-coreMain')]//div[hasclass('o-mail-Composer-toolButtons')]" position="inside">
            <button t-if="canSendWhatsApp"
                    class="btn btn-sm btn-outline-success o-mail-Composer-whatsappButton"
                    type="button"
                    t-on-click="sendViaWhatsApp"
                    title="Send via WhatsApp (Ctrl+Shift+W)"
                    data-hotkey="ctrl+shift+w">
                <i class="fa fa-whatsapp me-1"/>
                <span class="d-none d-sm-inline">WhatsApp</span>
            </button>
        </xpath>
        
        <!-- Alternative placement if the above doesn't work - add after send button -->
        <xpath expr="//button[hasclass('o-mail-Composer-send')]" position="after">
            <button t-if="canSendWhatsApp and !ui.isSmall"
                    class="btn btn-sm btn-outline-success o-mail-Composer-whatsappButton ms-1"
                    type="button"
                    t-on-click="sendViaWhatsApp"
                    title="Send via WhatsApp (Ctrl+Shift+W)"
                    data-hotkey="ctrl+shift+w">
                <i class="fa fa-whatsapp me-1"/>
                WhatsApp
            </button>
        </xpath>
        
        <!-- Mobile version - smaller button -->
        <xpath expr="//button[hasclass('o-mail-Composer-send')]" position="after">
            <button t-if="canSendWhatsApp and ui.isSmall"
                    class="btn btn-sm btn-outline-success o-mail-Composer-whatsappButton ms-1"
                    type="button"
                    t-on-click="sendViaWhatsApp"
                    title="Send via WhatsApp">
                <i class="fa fa-whatsapp"/>
            </button>
        </xpath>
    </t>
</templates>
