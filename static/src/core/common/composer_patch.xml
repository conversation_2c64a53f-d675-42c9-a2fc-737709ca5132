<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="whatsapp.Composer" t-inherit="mail.Composer" t-inherit-mode="extension">
        <!-- Temporarily disable composer actions modification for Odoo 18 compatibility -->
        <!--
        <xpath expr="//div[contains(@class, 'o-mail-Composer-actions')]" position="attributes">
            <attribute name="t-if">!thread || thread.channel_type !== 'whatsapp' || state.active</attribute>
        </xpath>
        -->
        <!-- Temporarily disable FileUploader modifications for Odoo 18 compatibility -->
        <!--
        <xpath expr="//FileUploader" position="attributes">
            <attribute name="multiUpload">thread and thread.channel_type === 'whatsapp' ? false : true</attribute>
        </xpath>
        <xpath expr="//FileUploader/t/button" position="attributes">
            <attribute name="t-att-disabled" add="or (thread and thread.channel_type === 'whatsapp' and props.composer.attachments.length > 0)" separator=" " />
        </xpath>
        -->
    </t>
</templates>
