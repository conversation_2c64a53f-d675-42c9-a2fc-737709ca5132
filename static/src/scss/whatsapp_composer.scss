/* WhatsApp Composer Styles */

.o-mail-Composer-whatsappButton {
    border-color: #25D366 !important;
    color: #25D366 !important;
    transition: all 0.2s ease;
    
    &:hover {
        background-color: #25D366 !important;
        color: white !important;
        border-color: #25D366 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(37, 211, 102, 0.3);
    }
    
    &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(37, 211, 102, 0.3);
    }
    
    &:focus {
        box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
        border-color: #25D366 !important;
    }
    
    .fa-whatsapp {
        color: inherit;
    }
}

/* WhatsApp Direct Message Form Styles */
.o_form_view .oe_title h1 .fa-whatsapp {
    color: #25D366;
}

/* WhatsApp message status indicators */
.alert .fa-whatsapp {
    color: #25D366;
}

.alert-success .fa-check-circle {
    color: #155724;
}

.alert-info .fa-spinner {
    color: #0c5460;
}

/* Mobile responsive adjustments */
@media (max-width: 576px) {
    .o-mail-Composer-whatsappButton {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        
        .fa-whatsapp {
            font-size: 1rem;
        }
    }
}

/* Chat window specific styles */
.o-mail-ChatWindow .o-mail-Composer-whatsappButton {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
}

/* Discuss app specific styles */
.o-mail-DiscussApp .o-mail-Composer-whatsappButton {
    margin-left: 0.5rem;
}

/* Animation for button state changes */
.o-mail-Composer-whatsappButton {
    position: relative;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }
    
    &:active::before {
        width: 100px;
        height: 100px;
    }
}

/* Tooltip styling */
.o-mail-Composer-whatsappButton[title] {
    position: relative;
}

/* Success notification styling */
.o_notification.o_notification_success .fa-whatsapp {
    color: #25D366;
    margin-right: 0.5rem;
}

/* Error notification styling */
.o_notification.o_notification_danger .fa-whatsapp {
    color: #dc3545;
    margin-right: 0.5rem;
}
