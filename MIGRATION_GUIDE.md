# WhatsApp Integration Migration Guide: Meta → Taqnyat.sa

## Overview

This guide covers the migration from Meta WhatsApp Business API to Taqnyat.sa WhatsApp Business API in your Odoo system.

## What Changed

### API Provider Migration
- **From**: Meta WhatsApp Business API (`https://graph.facebook.com/v17.0`)
- **To**: Taqnyat.sa WhatsApp Business API (`https://api.taqnyat.sa/wa/v2/`)

### Authentication Simplification
- **Before**: Complex setup with App ID, App Secret, Account ID, Phone Number ID, and Access Token
- **After**: Simple Bearer token from Taqnyat.sa portal

### Key Benefits
1. **Simplified Setup**: Only requires a Bearer token from Taqnyat.sa
2. **Better Phone Number Support**: Automatic handling of international formats
3. **Reliable Delivery**: Improved message delivery rates
4. **Cost Effective**: Competitive pricing structure
5. **Local Support**: Arabic documentation and local support

## Migration Steps

### 1. Get Taqnyat.sa Bearer Token

1. Visit [Taqnyat.sa Portal](https://portal.taqnyat.sa)
2. Login to your account or create a new one
3. Navigate to Developer section → Applications
4. Create a new application for WhatsApp service
5. Copy the Bearer token

### 2. Update WhatsApp Account Configuration

1. Go to **Settings → WhatsApp → WhatsApp Business Accounts**
2. Open your existing account or create a new one
3. In the "Taqnyat.sa WhatsApp Business API Configuration" section:
   - Paste your Bearer token in the "Bearer Token" field
4. Click "Test Connection" to verify the setup
5. Click "Synchronize Templates" to sync your templates

### 3. Webhook Configuration (Optional)

If you're using webhooks for receiving messages:
1. Update your webhook URL in Taqnyat.sa portal to point to: `https://yourdomain.com/whatsapp/webhook/`
2. The system will automatically handle both Taqnyat.sa and Meta webhook formats

### 4. Test the Integration

1. Send a test template message
2. Verify message delivery status
3. Test file attachments if used
4. Verify inbound message handling (if applicable)

## Backward Compatibility

The migration maintains full backward compatibility:
- Existing Meta API configurations continue to work
- All existing templates and messages are preserved
- No data loss during migration
- Gradual migration is supported

## Phone Number Format

Taqnyat.sa API automatically handles phone number formatting:
- Removes `+` or `00` prefixes as required
- Supports international formats
- Better validation for Saudi and regional numbers

## API Endpoint Changes

### Message Sending
- **Meta**: `/{phone_uid}/messages`
- **Taqnyat**: `/messages/`

### Media Upload
- **Meta**: `/{phone_uid}/media`
- **Taqnyat**: `/media/`

### Template Management
- **Meta**: `/{account_uid}/message_templates`
- **Taqnyat**: `/templates/`

### Balance Check
- **Meta**: Not available
- **Taqnyat**: `/account/balance?prodacts=1`

## Error Code Mapping

The system automatically maps error codes between APIs:

### Taqnyat.sa Error Codes
- `100`: Invalid parameter
- `401`: Authentication error
- `402`: Invalid recipient
- `132000`: Parameter count mismatch
- `132001`: Template not found

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify Bearer token is correct
   - Check token permissions in Taqnyat.sa portal

2. **Template Not Found**
   - Sync templates using "Synchronize Templates" button
   - Verify template status in Taqnyat.sa portal

3. **Message Delivery Failed**
   - Check account balance
   - Verify phone number format
   - Check recipient WhatsApp capability

### Support

- **Taqnyat.sa Support**: <EMAIL>
- **Documentation**: https://dev.taqnyat.sa/ar/doc/whatsapp/
- **Portal**: https://portal.taqnyat.sa

## Technical Details

### Files Modified
- `tools/whatsapp_api.py`: Core API implementation
- `models/whatsapp_account.py`: Account model updates
- `controller/main.py`: Webhook handling
- `tools/retryable_codes.py`: Error code updates
- `tools/bounced_codes.py`: Bounced error codes
- `views/whatsapp_account_views.xml`: UI updates

### New Features
- Automatic phone number format handling
- Simplified authentication
- Better error handling
- Support for Taqnyat.sa webhook format
- Balance checking capability

## Migration Checklist

- [ ] Obtained Taqnyat.sa Bearer token
- [ ] Updated account configuration
- [ ] Tested connection
- [ ] Synchronized templates
- [ ] Tested message sending
- [ ] Verified file attachments
- [ ] Updated webhook configuration (if used)
- [ ] Tested inbound messages (if applicable)
- [ ] Verified error handling
- [ ] Updated documentation for users

## Next Steps

After successful migration:
1. Monitor message delivery rates
2. Update user documentation
3. Train users on any new features
4. Consider disabling legacy Meta API fields if not needed
5. Set up monitoring for account balance

For technical support or questions about this migration, please contact your system administrator or Taqnyat.sa support team.
