# Part of Odoo. See LICENSE file for full copyright and licensing details.

import json
from unittest.mock import patch, MagicMock

from odoo.tests import tagged
from odoo.addons.whatsapp.tests.common import WhatsAppCommon
from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError


@tagged('whatsapp', 'post_install', '-at_install')
class TestTaqnyatMigration(WhatsAppCommon):
    """Test Taqnyat.sa API migration functionality"""

    def setUp(self):
        super().setUp()
        # Create a Taqnyat.sa account
        self.taqnyat_account = self.env['whatsapp.account'].create({
            'name': 'Taqnyat Test Account',
            'token': 'test_bearer_token_123456789',
            'notify_user_ids': [(6, 0, [self.env.user.id])],
        })

    def test_taqnyat_api_initialization(self):
        """Test that Taqnyat.sa API initializes correctly"""
        api = WhatsAppApi(self.taqnyat_account)
        self.assertEqual(api.token, 'test_bearer_token_123456')
        self.assertEqual(api.wa_account_id, self.taqnyat_account)

    @patch('requests.request')
    def test_taqnyat_connection_test_success(self, mock_request):
        """Test successful connection to Taqnyat.sa API"""
        # Mock successful templates response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {'data': []}
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        result = api._test_connection()
        self.assertTrue(result)

        # Verify the request was made correctly
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], 'GET')
        self.assertIn('/templates/', args[1])
        self.assertIn('Authorization', kwargs['headers'])
        self.assertEqual(kwargs['headers']['Authorization'], 'test_bearer_token_123456')

    @patch('requests.request')
    def test_taqnyat_connection_test_invalid_token_401(self, mock_request):
        """Test connection with invalid token (401 error)"""
        # Mock 401 authentication error
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.text = '{"error": "Invalid token"}'
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)

        with self.assertRaises(WhatsAppError) as context:
            api._test_connection()

        self.assertIn("Authentication failed", str(context.exception))
        self.assertIn("Invalid Bearer token", str(context.exception))

    @patch('requests.request')
    def test_taqnyat_connection_test_forbidden_403(self, mock_request):
        """Test connection with token lacking permissions (403 error)"""
        # Mock 403 access forbidden error
        mock_response = MagicMock()
        mock_response.status_code = 403
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.text = '{"error": "Access forbidden"}'
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)

        with self.assertRaises(WhatsAppError) as context:
            api._test_connection()

        self.assertIn("Access forbidden", str(context.exception))
        self.assertIn("does not have permission", str(context.exception))

    @patch('requests.request')
    def test_taqnyat_connection_test_html_response(self, mock_request):
        """Test connection when API returns HTML (API not activated)"""
        # Mock HTML response (common when API access is not activated)
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'text/html'}
        mock_response.text = '<html><body>Login page</body></html>'
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)

        with self.assertRaises(WhatsAppError) as context:
            api._test_connection()

        self.assertIn("API returned HTML instead of JSON", str(context.exception))
        self.assertIn("Bearer token is not activated", str(context.exception))

    @patch('requests.request')
    def test_taqnyat_message_sending(self, mock_request):
        """Test message sending with Taqnyat.sa API"""
        # Mock successful message send response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'type': 'template',
            'statuses': {
                'message_id': 'wamid.test123456789',
                'recipient': '************'
            }
        }
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        # Test template message
        send_vals = {
            'name': 'test_template',
            'language': {'code': 'ar'}
        }
        
        msg_uid = api._send_whatsapp(
            number='+************',
            message_type='template',
            send_vals=send_vals
        )
        
        self.assertEqual(msg_uid, 'wamid.test123456789')
        
        # Verify the request
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], 'POST')
        self.assertIn('/messages/', args[1])
        
        # Check request data
        request_data = json.loads(kwargs['data'])
        self.assertEqual(request_data['to'], '************')  # Should remove + prefix
        self.assertEqual(request_data['type'], 'template')

    @patch('requests.request')
    def test_taqnyat_media_upload(self, mock_request):
        """Test media upload with Taqnyat.sa API"""
        # Mock successful media upload response
        mock_response = MagicMock()
        mock_response.json.return_value = {'media_id': 'media_123456789'}
        mock_request.return_value = mock_response

        # Create a test attachment
        attachment = self.env['ir.attachment'].create({
            'name': 'test_document.pdf',
            'mimetype': 'application/pdf',
            'raw': b'test file content',
        })

        api = WhatsAppApi(self.taqnyat_account)
        media_id = api._upload_whatsapp_document(attachment)
        
        self.assertEqual(media_id, 'media_123456789')
        
        # Verify the request
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], 'POST')
        self.assertIn('/media/', args[1])
        self.assertIn('files', kwargs)

    def test_phone_number_formatting(self):
        """Test phone number formatting for Taqnyat.sa"""
        api = WhatsAppApi(self.taqnyat_account)
        
        # Test various phone number formats
        test_cases = [
            ('+************', '************'),
            ('00************', '************'),
            ('************', '************'),
        ]
        
        for input_number, expected in test_cases:
            with patch.object(api, '__api_requests') as mock_request:
                mock_request.return_value.json.return_value = {
                    'statuses': {'message_id': 'test123'}
                }
                
                api._send_whatsapp(
                    number=input_number,
                    message_type='text',
                    send_vals={'body': 'test'}
                )
                
                # Check that the number was formatted correctly
                call_data = json.loads(mock_request.call_args[1]['data'])
                self.assertEqual(call_data['to'], expected)

    def test_error_code_handling(self):
        """Test Taqnyat.sa error code handling"""
        from odoo.addons.whatsapp.tools.retryable_codes import WHATSAPP_RETRYABLE_ERROR_CODES
        from odoo.addons.whatsapp.tools.bounced_codes import BOUNCED_ERROR_CODES
        
        # Check that Taqnyat.sa error codes are included
        self.assertIn(100, WHATSAPP_RETRYABLE_ERROR_CODES)  # Invalid parameter
        self.assertIn(401, WHATSAPP_RETRYABLE_ERROR_CODES)  # Auth error
        self.assertIn(402, WHATSAPP_RETRYABLE_ERROR_CODES)  # Invalid recipient
        
        # Check bounced codes
        self.assertIn(402, BOUNCED_ERROR_CODES)  # Invalid recipient

    def test_webhook_processing(self):
        """Test Taqnyat.sa webhook processing"""
        # Test delivery report processing
        status_data = {
            'status': 'delivered',
            'state': 'delivered',
            'message_id': 'test_msg_123',
            'details': 'Message delivered successfully',
            'recipient': '************',
            'timestamp': '2024-01-01T12:00:00Z'
        }
        
        # Create a test message
        test_message = self.env['whatsapp.message'].create({
            'mobile_number': '************',
            'msg_uid': 'test_msg_123',
            'state': 'sent',
            'wa_account_id': self.taqnyat_account.id,
        })
        
        # Process the status
        result = self.env['whatsapp.message']._process_taqnyat_status(status_data)
        self.assertTrue(result)
        
        # Check that message state was updated
        test_message.refresh()
        self.assertEqual(test_message.state, 'delivered')

    def test_backward_compatibility(self):
        """Test that legacy Meta API fields are preserved"""
        # Create account with both Taqnyat and Meta fields
        mixed_account = self.env['whatsapp.account'].create({
            'name': 'Mixed API Account',
            'token': 'taqnyat_bearer_token',
            'app_uid': 'legacy_app_id',
            'app_secret': 'legacy_app_secret',
            'account_uid': 'legacy_account_id',
            'phone_uid': 'legacy_phone_id',
            'notify_user_ids': [(6, 0, [self.env.user.id])],
        })
        
        # Verify all fields are preserved
        self.assertEqual(mixed_account.token, 'taqnyat_bearer_token')
        self.assertEqual(mixed_account.app_uid, 'legacy_app_id')
        self.assertEqual(mixed_account.app_secret, 'legacy_app_secret')
        self.assertEqual(mixed_account.account_uid, 'legacy_account_id')
        self.assertEqual(mixed_account.phone_uid, 'legacy_phone_id')

    def test_template_synchronization(self):
        """Test template synchronization with Taqnyat.sa"""
        with patch.object(WhatsAppApi, '_get_all_template') as mock_get_templates:
            mock_get_templates.return_value = {
                'data': [
                    {
                        'id': 'taqnyat_template_1',
                        'name': 'welcome_message',
                        'language': 'ar',
                        'status': 'approved',
                        'category': 'marketing',
                        'components': []
                    }
                ]
            }
            
            # Test template sync
            result = self.taqnyat_account.button_sync_whatsapp_account_templates()
            
            # Verify the result
            self.assertEqual(result['type'], 'ir.actions.client')
            self.assertEqual(result['tag'], 'display_notification')
