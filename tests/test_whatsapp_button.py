# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tests import tagged
from odoo.addons.whatsapp.tests.common import WhatsAppCommon


@tagged('whatsapp')
class TestWhatsAppButton(WhatsAppCommon):
    """Test WhatsApp button functionality and availability checks"""

    def test_whatsapp_availability_with_taqnyat_account(self):
        """Test WhatsApp availability when Taqnyat.sa account is configured"""
        # Create a WhatsApp account with Taqnyat.sa token
        account = self.env['whatsapp.account'].create({
            'name': 'Test Taqnyat Account',
            'token': 'test_bearer_token_123',
            'allowed_company_ids': [(4, self.env.company.id)],
        })
        
        # Test account availability
        self.assertTrue(account.is_whatsapp_available())
        
        # Test template availability for res.partner model
        can_use = self.env['whatsapp.template']._can_use_whatsapp('res.partner')
        self.assertTrue(can_use, "Should be able to use WhatsApp with configured account")

    def test_whatsapp_availability_with_meta_account(self):
        """Test WhatsApp availability when Meta API account is configured"""
        # Create a WhatsApp account with Meta API credentials
        account = self.env['whatsapp.account'].create({
            'name': 'Test Meta Account',
            'app_uid': 'test_app_id',
            'app_secret': 'test_app_secret',
            'account_uid': 'test_account_id',
            'phone_uid': 'test_phone_id',
            'allowed_company_ids': [(4, self.env.company.id)],
        })
        
        # Test account availability
        self.assertTrue(account.is_whatsapp_available())

    def test_whatsapp_unavailable_without_account(self):
        """Test WhatsApp unavailability when no account is configured"""
        # Ensure no WhatsApp accounts exist for current company
        accounts = self.env['whatsapp.account'].search([
            ('allowed_company_ids', 'in', self.env.company.ids)
        ])
        accounts.unlink()
        
        # Test account availability
        is_available = self.env['whatsapp.account'].is_whatsapp_available()
        self.assertFalse(is_available, "WhatsApp should not be available without configured account")
        
        # Test template availability
        can_use = self.env['whatsapp.template']._can_use_whatsapp('res.partner')
        self.assertFalse(can_use, "Should not be able to use WhatsApp without configured account")

    def test_whatsapp_unavailable_with_incomplete_account(self):
        """Test WhatsApp unavailability when account is incompletely configured"""
        # Create a WhatsApp account without proper authentication
        account = self.env['whatsapp.account'].create({
            'name': 'Incomplete Account',
            'allowed_company_ids': [(4, self.env.company.id)],
            # No token or Meta API credentials
        })
        
        # Test account availability
        self.assertFalse(account.is_whatsapp_available())

    def test_whatsapp_availability_with_template_access(self):
        """Test WhatsApp availability with proper template access"""
        # Create account and template
        account = self.env['whatsapp.account'].create({
            'name': 'Test Account',
            'token': 'test_token',
            'allowed_company_ids': [(4, self.env.company.id)],
        })
        
        template = self.env['whatsapp.template'].create({
            'name': 'test_template',
            'body': 'Test message',
            'model': 'res.partner',
            'status': 'approved',
            'wa_account_id': account.id,
        })
        
        # Test with regular user
        can_use = self.env['whatsapp.template'].with_user(self.user_employee)._can_use_whatsapp('res.partner')
        self.assertTrue(can_use, "Employee should be able to use WhatsApp with approved template")

    def test_whatsapp_unavailable_without_template_access(self):
        """Test WhatsApp unavailability without template access"""
        # Create account but no templates
        account = self.env['whatsapp.account'].create({
            'name': 'Test Account',
            'token': 'test_token',
            'allowed_company_ids': [(4, self.env.company.id)],
        })
        
        # Test with regular user (no admin rights, no templates)
        can_use = self.env['whatsapp.template'].with_user(self.user_employee)._can_use_whatsapp('res.partner')
        self.assertFalse(can_use, "Employee should not be able to use WhatsApp without templates or admin rights")

    def test_whatsapp_admin_always_has_access(self):
        """Test that WhatsApp admin always has access when account is configured"""
        # Create account
        account = self.env['whatsapp.account'].create({
            'name': 'Test Account',
            'token': 'test_token',
            'allowed_company_ids': [(4, self.env.company.id)],
        })
        
        # Test with WhatsApp admin user
        can_use = self.env['whatsapp.template'].with_user(self.user_wa_admin)._can_use_whatsapp('res.partner')
        self.assertTrue(can_use, "WhatsApp admin should always have access when account is configured")
