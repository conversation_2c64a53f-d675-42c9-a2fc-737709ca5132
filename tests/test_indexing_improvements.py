# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
from odoo.tests import TransactionCase, tagged
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


@tagged('post_install', '-at_install')
class TestWhatsAppIndexingImprovements(TransactionCase):
    """Test WhatsApp module indexing improvements and performance tracking"""

    def setUp(self):
        super().setUp()
        self.WhatsAppAccount = self.env['whatsapp.account']
        self.WhatsAppMessage = self.env['whatsapp.message']
        self.WhatsAppTemplate = self.env['whatsapp.template']

    def test_database_indexes_exist(self):
        """Test that database indexes are properly created"""
        # Check if indexes exist by querying PostgreSQL system tables
        index_queries = [
            "SELECT 1 FROM pg_indexes WHERE indexname = 'whatsapp_account_token_partial_idx'",
            "SELECT 1 FROM pg_indexes WHERE indexname = 'whatsapp_account_active_company_idx'",
            "SELECT 1 FROM pg_indexes WHERE indexname = 'whatsapp_account_search_keywords_idx'",
            "SELECT 1 FROM pg_indexes WHERE indexname = 'whatsapp_message_account_state_idx'",
            "SELECT 1 FROM pg_indexes WHERE indexname = 'whatsapp_message_msg_uid_idx'",
            "SELECT 1 FROM pg_indexes WHERE indexname = 'whatsapp_template_account_status_idx'",
        ]
        
        for query in index_queries:
            self.env.cr.execute(query)
            result = self.env.cr.fetchone()
            self.assertTrue(result, f"Index not found for query: {query}")

    def test_performance_view_exists(self):
        """Test that performance monitoring view exists"""
        self.env.cr.execute("SELECT 1 FROM pg_views WHERE viewname = 'whatsapp_performance_stats'")
        result = self.env.cr.fetchone()
        self.assertTrue(result, "Performance stats view not found")

    def test_account_performance_fields(self):
        """Test WhatsApp account performance tracking fields"""
        # Create a test account
        account = self.WhatsAppAccount.create({
            'name': 'Test Account',
            'token': 'test_token_123456789',
        })
        
        # Check that performance fields are properly initialized
        self.assertIsNotNone(account.last_api_reset)
        self.assertEqual(account.api_call_count, 0)
        self.assertIsNotNone(account.search_keywords)
        
        # Test API counter increment
        initial_count = account.api_call_count
        account._increment_api_counter()
        self.assertEqual(account.api_call_count, initial_count + 1)

    def test_message_performance_tracking(self):
        """Test WhatsApp message performance tracking"""
        # Create a test account
        account = self.WhatsAppAccount.create({
            'name': 'Test Account',
            'token': 'test_token_123456789',
        })
        
        # Create a test message
        message = self.WhatsAppMessage.create({
            'mobile_number': '+************',
            'wa_account_id': account.id,
            'body': 'Test message',
            'state': 'outgoing',
        })
        
        # Check that performance fields exist
        self.assertIsNotNone(message.send_duration)
        self.assertIsNotNone(message.delivery_duration)

    def test_search_keywords_computation(self):
        """Test search keywords computation for accounts"""
        # Create a test account with Taqnyat token
        account = self.WhatsAppAccount.create({
            'name': 'Taqnyat Test Account',
            'token': 'taqnyat_token_123456789',
        })
        
        # Check that search keywords are computed
        self.assertIn('taqnyat', account.search_keywords.lower())
        self.assertIn('test', account.search_keywords.lower())
        self.assertIn('taqny', account.search_keywords.lower())  # First 5 chars of token
        
        # Create a test account with Meta credentials
        meta_account = self.WhatsAppAccount.create({
            'name': 'Meta Test Account',
            'app_uid': 'test_app_id',
            'app_secret': 'test_app_secret',
            'account_uid': 'test_account_id',
            'phone_uid': 'test_phone_id',
        })
        
        # Check that search keywords include Meta-specific terms
        self.assertIn('meta', meta_account.search_keywords.lower())
        self.assertIn('facebook', meta_account.search_keywords.lower())

    def test_api_counter_reset(self):
        """Test daily API counter reset functionality"""
        # Create test accounts
        account1 = self.WhatsAppAccount.create({
            'name': 'Test Account 1',
            'token': 'test_token_1',
            'api_call_count': 50,
        })
        
        account2 = self.WhatsAppAccount.create({
            'name': 'Test Account 2',
            'token': 'test_token_2',
            'api_call_count': 100,
        })
        
        # Reset counters
        self.WhatsAppAccount._reset_daily_api_counter()
        
        # Refresh records
        account1.refresh()
        account2.refresh()
        
        # Check that counters are reset
        self.assertEqual(account1.api_call_count, 0)
        self.assertEqual(account2.api_call_count, 0)

    def test_performance_cron_jobs_exist(self):
        """Test that performance-related cron jobs are created"""
        # Check for API counter reset cron job
        reset_cron = self.env.ref('whatsapp.ir_cron_reset_whatsapp_api_counters', raise_if_not_found=False)
        self.assertTrue(reset_cron, "API counter reset cron job not found")
        self.assertTrue(reset_cron.active, "API counter reset cron job is not active")
        
        # Check for performance cleanup cron job
        cleanup_cron = self.env.ref('whatsapp.ir_cron_cleanup_whatsapp_performance', raise_if_not_found=False)
        self.assertTrue(cleanup_cron, "Performance cleanup cron job not found")
        self.assertTrue(cleanup_cron.active, "Performance cleanup cron job is not active")

    def test_performance_views_accessible(self):
        """Test that performance views are accessible"""
        # Test performance dashboard action
        dashboard_action = self.env.ref('whatsapp.whatsapp_performance_dashboard_action', raise_if_not_found=False)
        self.assertTrue(dashboard_action, "Performance dashboard action not found")
        
        # Test account performance action
        account_action = self.env.ref('whatsapp.whatsapp_account_performance_action', raise_if_not_found=False)
        self.assertTrue(account_action, "Account performance action not found")

    def test_delivery_duration_tracking(self):
        """Test delivery duration tracking in status updates"""
        # Create a test account
        account = self.WhatsAppAccount.create({
            'name': 'Test Account',
            'token': 'test_token_123456789',
        })
        
        # Create a test message
        message = self.WhatsAppMessage.create({
            'mobile_number': '+************',
            'wa_account_id': account.id,
            'body': 'Test message',
            'state': 'sent',
            'msg_uid': 'test_msg_123',
        })
        
        # Simulate status update using Taqnyat format
        status_data = {
            'message_id': 'test_msg_123',
            'state': 'delivered'
        }
        
        # Process status update
        result = self.WhatsAppMessage._process_taqnyat_status(status_data)
        
        # Refresh message
        message.refresh()
        
        # Check that state is updated
        self.assertEqual(message.state, 'delivered')
        
        # Check that delivery duration is tracked (should be set)
        self.assertIsNotNone(message.delivery_duration)

    def test_webhook_performance_optimization(self):
        """Test that webhook processing is optimized with indexes"""
        # Create test messages with different UIDs
        account = self.WhatsAppAccount.create({
            'name': 'Test Account',
            'token': 'test_token_123456789',
        })
        
        messages = []
        for i in range(10):
            message = self.WhatsAppMessage.create({
                'mobile_number': f'+***********{i}',
                'wa_account_id': account.id,
                'body': f'Test message {i}',
                'state': 'sent',
                'msg_uid': f'test_msg_{i}',
            })
            messages.append(message)
        
        # Test that we can efficiently find messages by UID
        for message in messages:
            found_message = self.WhatsAppMessage.search([('msg_uid', '=', message.msg_uid)])
            self.assertEqual(len(found_message), 1)
            self.assertEqual(found_message.id, message.id)

    def test_template_indexing(self):
        """Test template-related indexing improvements"""
        # Create a test account
        account = self.WhatsAppAccount.create({
            'name': 'Test Account',
            'token': 'test_token_123456789',
        })
        
        # Create test templates
        template = self.WhatsAppTemplate.create({
            'name': 'Test Template',
            'template_name': 'test_template',
            'wa_account_id': account.id,
            'status': 'approved',
            'lang_code': 'en',
            'wa_template_uid': 'template_uid_123',
        })
        
        # Test that template can be found efficiently by various criteria
        found_by_uid = self.WhatsAppTemplate.search([('wa_template_uid', '=', 'template_uid_123')])
        self.assertEqual(len(found_by_uid), 1)
        
        found_by_name_lang = self.WhatsAppTemplate.search([
            ('template_name', '=', 'test_template'),
            ('lang_code', '=', 'en'),
            ('wa_account_id', '=', account.id)
        ])
        self.assertEqual(len(found_by_name_lang), 1)
