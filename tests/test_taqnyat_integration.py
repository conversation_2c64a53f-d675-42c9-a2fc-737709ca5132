# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Comprehensive Test Suite for Taqnyat WhatsApp Integration
Tests for all components of the enhanced WhatsApp integration
"""

import json
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
from odoo.addons.whatsapp.tools.taqnyat_config import TaqnyatConfig, MESSAGE_TYPES
from odoo.addons.whatsapp.tools.phone_validator import PhoneNumberValidator, OptInManager
from odoo.addons.whatsapp.tools.session_manager import SessionWindowManager
from odoo.addons.whatsapp.tools.message_builder import TaqnyatMessageBuilder


class TestTaqnyatIntegration(TransactionCase):
    """
    Main test class for Taqnyat integration
    """
    
    def setUp(self):
        super().setUp()
        
        # Create test WhatsApp account
        self.wa_account = self.env['whatsapp.account'].create({
            'name': 'Test Taqnyat Account',
            'account_uid': 'test_account_123',
            'token': 'Bearer test_token_123',
            'phone_uid': '************',
            'active': True
        })
        
        # Test phone numbers
        self.test_phone_sa = '************'
        self.test_phone_ae = '************'
        self.test_phone_invalid = '123456'
        
        # Create test partner
        self.test_partner = self.env['res.partner'].create({
            'name': 'Test Contact',
            'mobile': self.test_phone_sa,
            'customer_rank': 1
        })

class TestPhoneNumberValidation(TestTaqnyatIntegration):
    """
    Test phone number validation functionality
    """
    
    def test_clean_phone_number(self):
        """Test phone number cleaning"""
        # Test various formats
        test_cases = [
            ('+************', '************'),
            ('00************', '************'),
            ('966-50-123-4567', '************'),
            ('966 50 123 4567', '************'),
            ('(966) 50-123-4567', '************')
        ]
        
        for input_phone, expected in test_cases:
            with self.subTest(input_phone=input_phone):
                result = PhoneNumberValidator.clean_phone_number(input_phone)
                self.assertEqual(result, expected)
    
    def test_validate_phone_format(self):
        """Test phone number format validation"""
        # Valid numbers
        valid_numbers = [
            '************',  # Saudi Arabia
            '************',  # UAE
            '20101234567',   # Egypt
        ]
        
        for phone in valid_numbers:
            with self.subTest(phone=phone):
                is_valid, cleaned, country = PhoneNumberValidator.validate_phone_format(phone)
                self.assertTrue(is_valid)
                self.assertEqual(cleaned, phone)
        
        # Invalid numbers
        invalid_numbers = [
            '123456',        # Too short
            '12345678901234567',  # Too long
            'abc123456789',  # Contains letters
        ]
        
        for phone in invalid_numbers:
            with self.subTest(phone=phone):
                is_valid, _, _ = PhoneNumberValidator.validate_phone_format(phone)
                self.assertFalse(is_valid)
    
    def test_country_detection(self):
        """Test country code detection"""
        test_cases = [
            ('************', 'SA'),
            ('************', 'AE'),
            ('20101234567', 'EG'),
            ('962781234567', 'JO'),
        ]
        
        for phone, expected_country in test_cases:
            with self.subTest(phone=phone):
                country = PhoneNumberValidator.detect_country(phone)
                self.assertEqual(country, expected_country)

class TestOptInManagement(TestTaqnyatIntegration):
    """
    Test opt-in/opt-out management
    """
    
    def test_record_opt_in(self):
        """Test recording opt-in"""
        manager = OptInManager(self.env)
        
        # Record opt-in
        result = manager.record_opt_in(
            self.test_phone_sa, 
            self.wa_account.id, 
            'test', 
            True
        )
        
        self.assertTrue(result)
        
        # Check opt-in record created
        opt_in = self.env['whatsapp.optin'].search([
            ('mobile_number', '=', self.test_phone_sa),
            ('wa_account_id', '=', self.wa_account.id)
        ])
        
        self.assertTrue(opt_in)
        self.assertTrue(opt_in.is_opted_in)
        self.assertEqual(opt_in.source, 'test')
    
    def test_record_opt_out(self):
        """Test recording opt-out"""
        manager = OptInManager(self.env)
        
        # First opt-in
        manager.record_opt_in(self.test_phone_sa, self.wa_account.id)
        
        # Then opt-out
        result = manager.record_opt_out(
            self.test_phone_sa, 
            self.wa_account.id, 
            'user_request'
        )
        
        self.assertTrue(result)
        
        # Check opt-out recorded
        opt_in = self.env['whatsapp.optin'].search([
            ('mobile_number', '=', self.test_phone_sa),
            ('wa_account_id', '=', self.wa_account.id)
        ])
        
        self.assertFalse(opt_in.is_opted_in)
        self.assertEqual(opt_in.opt_out_reason, 'user_request')
    
    def test_can_send_message(self):
        """Test message sending permission check"""
        manager = OptInManager(self.env)
        
        # Test without opt-in
        can_send, reason = manager.can_send_message(
            self.test_phone_sa, 
            self.wa_account.id, 
            'marketing'
        )
        self.assertFalse(can_send)
        
        # Test with opt-in
        manager.record_opt_in(self.test_phone_sa, self.wa_account.id)
        can_send, reason = manager.can_send_message(
            self.test_phone_sa, 
            self.wa_account.id, 
            'marketing'
        )
        self.assertTrue(can_send)
        
        # Test utility messages (should be allowed even without marketing opt-in)
        can_send, reason = manager.can_send_message(
            self.test_phone_ae, 
            self.wa_account.id, 
            'utility'
        )
        self.assertTrue(can_send)

class TestSessionWindowManagement(TestTaqnyatIntegration):
    """
    Test 24-hour session window management
    """
    
    def test_session_window_tracking(self):
        """Test session window tracking"""
        manager = SessionWindowManager(self.env)
        
        # Test new contact (no session)
        is_active = manager.is_session_active(self.test_phone_sa, self.wa_account.id)
        self.assertFalse(is_active)
        
        # Simulate inbound message
        manager.update_last_inbound_message(
            self.test_phone_sa, 
            self.wa_account.id, 
            'test_msg_123'
        )
        
        # Check session is now active
        is_active = manager.is_session_active(self.test_phone_sa, self.wa_account.id)
        self.assertTrue(is_active)
    
    def test_message_type_recommendation(self):
        """Test message type recommendation"""
        manager = SessionWindowManager(self.env)
        
        # Test without session
        recommendation = manager.get_message_type_recommendation(
            self.test_phone_sa, 
            self.wa_account.id
        )
        self.assertEqual(recommendation['recommended_type'], 'template')
        self.assertFalse(recommendation['session_active'])
        
        # Test with active session
        manager.update_last_inbound_message(
            self.test_phone_sa, 
            self.wa_account.id, 
            'test_msg_123'
        )
        
        recommendation = manager.get_message_type_recommendation(
            self.test_phone_sa, 
            self.wa_account.id
        )
        self.assertEqual(recommendation['recommended_type'], 'conversation')
        self.assertTrue(recommendation['session_active'])

class TestMessageBuilder(TestTaqnyatIntegration):
    """
    Test message building functionality
    """
    
    def test_build_text_message(self):
        """Test text message building"""
        message = TaqnyatMessageBuilder.build_text_message("Hello World", True)
        
        self.assertEqual(message['body'], "Hello World")
        self.assertTrue(message['preview_url'])
        
        # Test empty message
        with self.assertRaises(ValidationError):
            TaqnyatMessageBuilder.build_text_message("")
    
    def test_build_template_message(self):
        """Test template message building"""
        message = TaqnyatMessageBuilder.build_template_message(
            "hello_world", 
            "en", 
            [{"type": "body", "parameters": [{"type": "text", "text": "John"}]}]
        )
        
        self.assertEqual(message['name'], "hello_world")
        self.assertEqual(message['language']['code'], "en")
        self.assertTrue(message['components'])
    
    def test_build_interactive_button_message(self):
        """Test interactive button message building"""
        buttons = [
            {
                "reply": {
                    "id": "btn_1",
                    "title": "Yes"
                }
            },
            {
                "reply": {
                    "id": "btn_2", 
                    "title": "No"
                }
            }
        ]
        
        message = TaqnyatMessageBuilder.build_interactive_button_message(
            "Do you want to continue?",
            buttons
        )
        
        self.assertEqual(message['type'], 'button')
        self.assertEqual(message['body']['text'], "Do you want to continue?")
        self.assertEqual(len(message['action']['buttons']), 2)
        
        # Test too many buttons
        too_many_buttons = [{"reply": {"id": f"btn_{i}", "title": f"Button {i}"}} for i in range(5)]
        with self.assertRaises(ValidationError):
            TaqnyatMessageBuilder.build_interactive_button_message(
                "Test", too_many_buttons
            )

class TestApiIntegration(TestTaqnyatIntegration):
    """
    Test API integration with mocked responses
    """
    
    @patch('requests.post')
    def test_send_message_api_call(self, mock_post):
        """Test API call for sending messages"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'message_id': 'test_msg_123',
            'status': 'queued'
        }
        mock_post.return_value = mock_response
        
        # Test API call
        from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
        api = WhatsAppApi(self.wa_account)
        
        # This would normally make an API call
        # We're testing the integration structure here
        self.assertTrue(hasattr(api, 'send_message_smart'))
        self.assertTrue(hasattr(api, 'upload_media_enhanced'))
        self.assertTrue(hasattr(api, 'get_session_status'))

if __name__ == '__main__':
    unittest.main()
