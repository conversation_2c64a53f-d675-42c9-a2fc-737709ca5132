# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tests import tagged
from odoo.tests.common import TransactionCase
from odoo.addons.whatsapp.tests.common import WhatsAppCommon


@tagged('whatsapp')
class TestGlobalPhoneIntegration(WhatsAppCommon):
    """Test global phone field integration functionality"""

    def setUp(self):
        super().setUp()
        # Create a WhatsApp account for testing
        self.whatsapp_account = self.env['whatsapp.account'].create({
            'name': 'Test Global Integration Account',
            'token': 'test_global_token_123',
            'allowed_company_ids': [(4, self.env.company.id)],
        })

    def test_global_integration_enabled_by_default(self):
        """Test that global integration is enabled by default"""
        param_value = self.env['ir.config_parameter'].get_param(
            'whatsapp.global_phone_integration', 'False'
        )
        self.assertEqual(param_value, 'True', "Global integration should be enabled by default")

    def test_global_integration_parameter_control(self):
        """Test that global integration can be controlled via system parameter"""
        # Disable global integration
        self.env['ir.config_parameter'].set_param('whatsapp.global_phone_integration', 'False')
        
        # Test that WhatsApp is not available when global integration is disabled
        can_use = self.env['whatsapp.template']._can_use_whatsapp('res.partner')
        self.assertFalse(can_use, "WhatsApp should not be available when global integration is disabled")
        
        # Re-enable global integration
        self.env['ir.config_parameter'].set_param('whatsapp.global_phone_integration', 'True')
        
        # Test that WhatsApp is available when global integration is enabled
        can_use = self.env['whatsapp.template']._can_use_whatsapp('res.partner')
        self.assertTrue(can_use, "WhatsApp should be available when global integration is enabled")

    def test_excluded_models_functionality(self):
        """Test that excluded models are properly handled"""
        # Set excluded models
        self.env['ir.config_parameter'].set_param('whatsapp.excluded_models', 'res.users,hr.employee')
        
        # Test that excluded models return False
        can_use_users = self.env['whatsapp.template']._can_use_whatsapp('res.users')
        self.assertFalse(can_use_users, "res.users should be excluded from WhatsApp integration")
        
        can_use_employee = self.env['whatsapp.template']._can_use_whatsapp('hr.employee')
        self.assertFalse(can_use_employee, "hr.employee should be excluded from WhatsApp integration")
        
        # Test that non-excluded models still work
        can_use_partner = self.env['whatsapp.template']._can_use_whatsapp('res.partner')
        self.assertTrue(can_use_partner, "res.partner should not be excluded from WhatsApp integration")

    def test_excluded_models_with_spaces(self):
        """Test that excluded models work with spaces in the parameter"""
        # Set excluded models with spaces
        self.env['ir.config_parameter'].set_param('whatsapp.excluded_models', ' res.users , hr.employee , ')
        
        # Test that excluded models return False even with spaces
        can_use_users = self.env['whatsapp.template']._can_use_whatsapp('res.users')
        self.assertFalse(can_use_users, "res.users should be excluded even with spaces in parameter")

    def test_cache_duration_parameter(self):
        """Test that cache duration parameter is properly set"""
        # Test default cache duration
        cache_duration = self.env['ir.config_parameter'].get_param(
            'whatsapp.availability_cache_duration', '0'
        )
        self.assertEqual(cache_duration, '300000', "Default cache duration should be 300000ms (5 minutes)")
        
        # Test setting custom cache duration
        self.env['ir.config_parameter'].set_param('whatsapp.availability_cache_duration', '600000')
        cache_duration = self.env['ir.config_parameter'].get_param('whatsapp.availability_cache_duration')
        self.assertEqual(cache_duration, '600000', "Custom cache duration should be properly set")

    def test_config_settings_integration(self):
        """Test that configuration settings work properly"""
        config = self.env['res.config.settings'].create({})
        
        # Test default values
        config._onchange_whatsapp_excluded_models()
        
        # Test setting values
        config.whatsapp_global_phone_integration = False
        config.whatsapp_excluded_models = 'res.users, hr.employee'
        config.whatsapp_availability_cache_duration = 600000
        
        config.set_values()
        
        # Verify parameters were set correctly
        global_enabled = self.env['ir.config_parameter'].get_param('whatsapp.global_phone_integration')
        self.assertEqual(global_enabled, 'False', "Global integration parameter should be set to False")
        
        excluded_models = self.env['ir.config_parameter'].get_param('whatsapp.excluded_models')
        self.assertEqual(excluded_models, 'res.users, hr.employee', "Excluded models should be set correctly")
        
        cache_duration = self.env['ir.config_parameter'].get_param('whatsapp.availability_cache_duration')
        self.assertEqual(cache_duration, '600000', "Cache duration should be set correctly")

    def test_excluded_models_cleanup(self):
        """Test that excluded models input is properly cleaned up"""
        config = self.env['res.config.settings'].create({
            'whatsapp_excluded_models': ' res.users , , hr.employee ,  , '
        })
        
        config._onchange_whatsapp_excluded_models()
        
        # Should clean up extra spaces and empty entries
        self.assertEqual(config.whatsapp_excluded_models, 'res.users, hr.employee',
                        "Excluded models should be cleaned up properly")

    def test_whatsapp_availability_with_admin_user(self):
        """Test that WhatsApp admin users always have access when account is configured"""
        # Test with WhatsApp admin user
        can_use = self.env['whatsapp.template'].with_user(self.user_wa_admin)._can_use_whatsapp('res.partner')
        self.assertTrue(can_use, "WhatsApp admin should always have access when account is configured")
        
        # Test with excluded model - admin should still be blocked
        self.env['ir.config_parameter'].set_param('whatsapp.excluded_models', 'res.partner')
        can_use = self.env['whatsapp.template'].with_user(self.user_wa_admin)._can_use_whatsapp('res.partner')
        self.assertFalse(can_use, "Even WhatsApp admin should be blocked for excluded models")

    def test_whatsapp_availability_without_account(self):
        """Test that WhatsApp is not available without configured account"""
        # Remove all WhatsApp accounts
        self.whatsapp_account.unlink()
        
        # Test that WhatsApp is not available
        can_use = self.env['whatsapp.template']._can_use_whatsapp('res.partner')
        self.assertFalse(can_use, "WhatsApp should not be available without configured account")
