# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import json
import logging
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from odoo.tests import tagged
from odoo.addons.whatsapp.tests.common import WhatsAppCommon
from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError

_logger = logging.getLogger(__name__)


@tagged('whatsapp', 'taqnyat', 'post_install', '-at_install')
class TestTaqnyatAPIIntegration(WhatsAppCommon):
    """
    Comprehensive test suite for Taqnyat WhatsApp API integration
    Based on Taqnyat.postman_collection (25).json
    """

    def setUp(self):
        super().setUp()
        # Create Taqnyat account for testing
        self.taqnyat_account = self.env['whatsapp.account'].create({
            'name': 'Taqnyat Test Account',
            'token': 'test_taqnyat_bearer_token_123456789',
            'notify_user_ids': [(6, 0, [self.env.user.id])],
        })
        
        # Test phone numbers (Saudi Arabia format as per Taqnyat)
        self.test_phone_sa = '************'
        self.test_phone_intl = '+************'
        
        # Create test partner
        self.test_partner = self.env['res.partner'].create({
            'name': 'Test Customer',
            'phone': self.test_phone_intl,
            'mobile': self.test_phone_intl,
        })

    # ========================================
    # 1. AUTHENTICATION & CONNECTION TESTS
    # ========================================

    @patch('requests.request')
    def test_bearer_token_authentication(self, mock_request):
        """Test Bearer token authentication as per Postman collection"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {'data': []}
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        result = api._test_connection()
        
        # Verify authentication header format
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertIn('Authorization', kwargs['headers'])
        # Should use direct token (not Bearer prefix) as per Postman line 669
        self.assertEqual(kwargs['headers']['Authorization'], 'test_taqnyat_bearer_token_123456789')
        self.assertTrue(result)

    @patch('requests.request')
    def test_invalid_token_handling(self, mock_request):
        """Test handling of invalid/expired tokens"""
        # Mock 401 Unauthorized response
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.text = '{"error": "Unauthorized", "message": "Invalid token"}'
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        with self.assertRaises(WhatsAppError) as context:
            api._test_connection()
        
        self.assertIn("Authentication failed", str(context.exception))
        self.assertIn("Invalid Bearer token", str(context.exception))

    # ========================================
    # 2. TEMPLATE MESSAGE TESTS
    # ========================================

    @patch('requests.request')
    def test_template_text_message(self, mock_request):
        """Test sending template text message (Postman: Template | Text)"""
        # Mock successful template response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {
            'type': 'template',
            'statuses': {
                'message_id': 'wamid.test_template_123456789',
                'recipient': '************'
            }
        }
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        # Send template message as per Postman collection
        send_vals = {
            'name': 'welcome_message',
            'language': {'code': 'en'}
        }
        
        msg_uid = api._send_whatsapp(
            number=self.test_phone_intl,
            message_type='template',
            send_vals=send_vals
        )
        
        self.assertEqual(msg_uid, 'wamid.test_template_123456789')
        
        # Verify request format matches Postman collection
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], 'POST')
        self.assertIn('/messages/', args[1])
        
        request_data = json.loads(kwargs['data'])
        self.assertEqual(request_data['to'], '************')  # Should remove + prefix
        self.assertEqual(request_data['type'], 'template')
        self.assertEqual(request_data['template']['name'], 'welcome_message')
        self.assertEqual(request_data['template']['language']['code'], 'en')

    @patch('requests.request')
    def test_template_with_variables(self, mock_request):
        """Test template with variables (Postman: Template | Interactive)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {
            'statuses': {'message_id': 'wamid.test_vars_123456789'}
        }
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        # Template with variables as per Postman collection
        send_vals = {
            'name': 'order_confirmation',
            'language': {'code': 'ar'},
            'components': [
                {
                    'type': 'body',
                    'parameters': [
                        {'type': 'text', 'text': 'John Doe'},
                        {'type': 'text', 'text': 'ORD-12345'},
                        {'type': 'text', 'text': '150.00 SAR'}
                    ]
                }
            ]
        }
        
        msg_uid = api._send_whatsapp(
            number=self.test_phone_intl,
            message_type='template',
            send_vals=send_vals
        )
        
        self.assertEqual(msg_uid, 'wamid.test_vars_123456789')

    @patch('requests.request')
    def test_otp_template_message(self, mock_request):
        """Test OTP template message (Postman: OTP)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {
            'statuses': {'message_id': 'wamid.test_otp_123456789'}
        }
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        # OTP template as per Postman collection
        otp_code = '123456'
        send_vals = {
            'name': 'otp_verification',
            'language': {'code': 'en'},
            'components': [
                {
                    'type': 'body',
                    'parameters': [
                        {'type': 'text', 'text': otp_code}
                    ]
                },
                {
                    'type': 'button',
                    'sub_type': 'url',
                    'index': '0',
                    'parameters': [
                        {'type': 'text', 'text': otp_code}
                    ]
                }
            ]
        }
        
        msg_uid = api._send_whatsapp(
            number=self.test_phone_intl,
            message_type='template',
            send_vals=send_vals
        )
        
        self.assertEqual(msg_uid, 'wamid.test_otp_123456789')

    # ========================================
    # 3. CONVERSATION MESSAGE TESTS
    # ========================================

    @patch('requests.request')
    def test_conversation_text_message(self, mock_request):
        """Test conversation text message (Postman: Conversation | Text)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {
            'statuses': {'message_id': 'wamid.test_text_123456789'}
        }
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        # Conversation text message as per Postman collection
        send_vals = {
            'body': 'Hello! Thank you for contacting us. How can we help you today?'
        }
        
        msg_uid = api._send_whatsapp(
            number=self.test_phone_intl,
            message_type='text',
            send_vals=send_vals
        )
        
        self.assertEqual(msg_uid, 'wamid.test_text_123456789')
        
        # Verify request format
        request_data = json.loads(mock_request.call_args[1]['data'])
        self.assertEqual(request_data['type'], 'text')
        self.assertEqual(request_data['text']['body'], send_vals['body'])

    @patch('requests.request')
    def test_conversation_image_message(self, mock_request):
        """Test conversation image message (Postman: Conversation | Image)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {
            'statuses': {'message_id': 'wamid.test_image_123456789'}
        }
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        # Image message as per Postman collection
        send_vals = {
            'link': 'https://www.example.com/image.jpg',
            'caption': 'Product catalog image',
            'filename': 'catalog.jpg',
            'provider': {'name': 'Odoo-Taqnyat'}
        }
        
        msg_uid = api._send_whatsapp(
            number=self.test_phone_intl,
            message_type='image',
            send_vals=send_vals
        )
        
        self.assertEqual(msg_uid, 'wamid.test_image_123456789')

    # ========================================
    # 4. OPT-IN COMPLIANCE TESTS
    # ========================================

    @patch('requests.request')
    def test_opt_in_sync(self, mock_request):
        """Test opt-in compliance check (Postman: Opt-In sync)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {
            'contacts': [
                {
                    'input': '************',
                    'status': 'valid',
                    'wa_id': '************'
                }
            ]
        }
        mock_request.return_value = mock_response

        # Test opt-in check before sending messages
        api = WhatsAppApi(self.taqnyat_account)
        
        # This would be a custom method to check opt-in status
        # Based on Postman: POST /wa/v2/contacts/
        contacts_data = {
            'blocking': 'wait',
            'contacts': [self.test_phone_sa],
            'force_check': True
        }
        
        # Simulate opt-in check
        response = api.__api_requests(
            'POST',
            '/contacts/',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(contacts_data)
        )
        
        self.assertEqual(response.status_code, 200)

    # ========================================
    # 5. ERROR HANDLING TESTS
    # ========================================

    @patch('requests.request')
    def test_400_bad_request_handling(self, mock_request):
        """Test handling of 400 Bad Request errors"""
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.text = '{"error": "Bad Request", "message": "Invalid phone number format"}'
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        with self.assertRaises(WhatsAppError) as context:
            api._send_whatsapp(
                number='invalid_number',
                message_type='text',
                send_vals={'body': 'Test message'}
            )
        
        self.assertIn("API request failed", str(context.exception))

    @patch('requests.request')
    def test_rate_limiting_handling(self, mock_request):
        """Test handling of rate limiting (429 Too Many Requests)"""
        mock_response = MagicMock()
        mock_response.status_code = 429
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.text = '{"error": "Rate limit exceeded"}'
        mock_request.return_value = mock_response

        api = WhatsAppApi(self.taqnyat_account)
        
        with self.assertRaises(WhatsAppError) as context:
            api._send_whatsapp(
                number=self.test_phone_intl,
                message_type='text',
                send_vals={'body': 'Test message'}
            )
        
        self.assertIn("API request failed", str(context.exception))

    # ========================================
    # 6. PHONE NUMBER FORMAT TESTS
    # ========================================

    def test_phone_number_formatting(self):
        """Test phone number formatting for Taqnyat API"""
        api = WhatsAppApi(self.taqnyat_account)
        
        # Test various phone number formats
        test_cases = [
            ('+************', '************'),
            ('00************', '************'),
            ('************', '************'),
            ('+**********', '**********'),
            ('00**********', '**********'),
        ]
        
        for input_number, expected in test_cases:
            with patch.object(api, '__api_requests') as mock_request:
                mock_request.return_value.json.return_value = {
                    'statuses': {'message_id': 'test123'}
                }
                
                api._send_whatsapp(
                    number=input_number,
                    message_type='text',
                    send_vals={'body': 'test'}
                )
                
                # Check that the number was formatted correctly
                call_data = json.loads(mock_request.call_args[1]['data'])
                self.assertEqual(call_data['to'], expected)

    # ========================================
    # 7. SESSION HANDLING TESTS
    # ========================================

    def test_24_hour_session_tracking(self):
        """Test 24-hour session handling for conversation messages"""
        # Create a WhatsApp message to simulate session start
        session_start = datetime.now() - timedelta(hours=12)  # 12 hours ago
        
        whatsapp_message = self.env['whatsapp.message'].create({
            'mobile_number': self.test_phone_sa,
            'mobile_number_formatted': self.test_phone_intl,
            'state': 'sent',
            'wa_account_id': self.taqnyat_account.id,
            'create_date': session_start,
        })
        
        # Test that we're still within 24-hour window
        time_diff = datetime.now() - session_start
        self.assertLess(time_diff.total_seconds(), 24 * 3600)  # Less than 24 hours
        
        # Test session expiry
        expired_session = datetime.now() - timedelta(hours=25)  # 25 hours ago
        whatsapp_message.write({'create_date': expired_session})
        
        time_diff = datetime.now() - expired_session
        self.assertGreater(time_diff.total_seconds(), 24 * 3600)  # More than 24 hours

    # ========================================
    # 8. MEDIA SIZE VALIDATION TESTS
    # ========================================

    def test_media_size_limits(self):
        """Test media file size limits (images: 5MB, documents: 100MB)"""
        # Test image size limit (5MB)
        image_size_mb = 5
        image_size_bytes = image_size_mb * 1024 * 1024
        
        # Test document size limit (100MB)
        document_size_mb = 100
        document_size_bytes = document_size_mb * 1024 * 1024
        
        # These would be actual file size validations in the real implementation
        self.assertLessEqual(image_size_bytes, 5 * 1024 * 1024)  # 5MB limit
        self.assertLessEqual(document_size_bytes, 100 * 1024 * 1024)  # 100MB limit

    # ========================================
    # 9. INTEGRATION TESTS
    # ========================================

    def test_odoo_whatsapp_composer_integration(self):
        """Test integration with Odoo WhatsApp composer wizard"""
        # Create a lead for testing
        lead = self.env['crm.lead'].create({
            'name': 'Test Lead',
            'partner_id': self.test_partner.id,
            'phone': self.test_phone_intl,
        })
        
        # Test composer wizard creation
        composer = self.env['whatsapp.composer'].with_context(
            active_model='crm.lead',
            active_id=lead.id,
            active_ids=[lead.id]
        ).create({
            'wa_account_id': self.taqnyat_account.id,
            'body': 'Hello! Thank you for your interest in our services.',
        })
        
        self.assertEqual(composer.wa_account_id, self.taqnyat_account)
        self.assertEqual(composer.body, 'Hello! Thank you for your interest in our services.')

    @patch('requests.request')
    def test_webhook_message_processing(self, mock_request):
        """Test processing of incoming webhook messages"""
        # Mock webhook payload from Taqnyat (similar to Meta format)
        webhook_data = {
            'entry': [{
                'id': 'test_entry_id',
                'changes': [{
                    'value': {
                        'contacts': [{
                            'profile': {'name': 'Test Customer'},
                            'wa_id': '************'
                        }],
                        'messages': [{
                            'from': '************',
                            'id': 'wamid.incoming_test_123456789',
                            'timestamp': '**********',
                            'text': {'body': 'Hello, I need help with my order'},
                            'type': 'text'
                        }]
                    },
                    'field': 'messages'
                }]
            }]
        }
        
        # Test message processing
        self.taqnyat_account._process_messages(webhook_data['entry'][0]['changes'][0]['value'])
        
        # Verify that a channel was created or found
        channel = self.taqnyat_account._find_active_channel('************', create_if_not_found=True)
        self.assertTrue(channel)

    def test_template_approval_workflow(self):
        """Test template approval workflow"""
        # Create a template for testing
        template = self.env['whatsapp.template'].create({
            'name': 'test_template',
            'wa_account_id': self.taqnyat_account.id,
            'status': 'pending',
            'body': 'Hello {{1}}, your order {{2}} is ready for pickup.',
            'language': 'en',
            'category': 'UTILITY',
        })
        
        self.assertEqual(template.status, 'pending')
        self.assertEqual(template.wa_account_id, self.taqnyat_account)
        
        # Test template approval (would be done via Taqnyat dashboard)
        template.write({'status': 'approved'})
        self.assertEqual(template.status, 'approved')
