#!/usr/bin/env python3
"""
Test script to verify the template body component fix for templates without variables
This script simulates the template message sending process to ensure static body text is sent correctly
"""

import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
_logger = logging.getLogger(__name__)

def mock_template_without_variables():
    """Mock template without variables (like 'hello')"""
    return {
        'body': 'hello',
        'variable_ids': [],  # No variables
        'template_name': 'test [odoo3]',
        'lang_code': 'ar'
    }

def mock_template_with_variables():
    """Mock template with variables (like 'Hello {{1}}, your order {{2}} is ready')"""
    return {
        'body': 'Hello {{1}}, your order {{2}} is ready',
        'variable_ids': [
            {'line_type': 'body', 'name': '{{1}}', 'field_type': 'user_name'},
            {'line_type': 'body', 'name': '{{2}}', 'field_type': 'order_number'}
        ],
        'template_name': 'order_ready',
        'lang_code': 'en'
    }

def old_get_body_component(template, free_text_json, template_variables_value):
    """OLD (BROKEN) implementation that returns None for templates without variables"""
    if not template['variable_ids']:
        return None  # ❌ This was the bug!
    
    parameters = []
    for body_val in template['variable_ids']:
        if body_val['line_type'] == 'body':
            parameters.append({
                'type': 'text',
                'text': template_variables_value.get(f"{body_val['line_type']}-{body_val['name']}", ' ')
            })
    return {'type': 'body', 'parameters': parameters}

def new_get_body_component(template, free_text_json, template_variables_value):
    """NEW (FIXED) implementation that handles templates without variables correctly"""
    # Check if template has variables in the body
    body_variables = [var for var in template['variable_ids'] if var['line_type'] == 'body']
    
    if not body_variables:
        # Template without variables - send static body text
        # This is for templates like "hello" without {{1}}, {{2}}, etc.
        if template['body']:
            return {
                'type': 'body',
                'parameters': [{
                    'type': 'text',
                    'text': template['body']
                }]
            }
        return None
    
    # Template with variables - process parameters
    parameters = []
    for body_val in body_variables:
        parameters.append({
            'type': 'text',
            'text': template_variables_value.get(f"{body_val['line_type']}-{body_val['name']}", ' ')
        })
    return {'type': 'body', 'parameters': parameters}

def simulate_template_sending(template, implementation_name, get_body_func):
    """Simulate the template sending process"""
    print(f"\n🧪 Testing {implementation_name}")
    print("=" * 50)
    
    print(f"📋 Template: '{template['template_name']}'")
    print(f"📝 Body: '{template['body']}'")
    print(f"🔢 Variables: {len(template['variable_ids'])}")
    
    # Mock data
    free_text_json = {}
    template_variables_value = {
        'body-{{1}}': 'John',
        'body-{{2}}': 'ORD-12345'
    }
    
    # Get body component
    body_component = get_body_func(template, free_text_json, template_variables_value)
    
    print(f"\n📤 Body Component Result:")
    if body_component is None:
        print("   ❌ None (no body will be sent!)")
        return None
    else:
        print(f"   ✅ {json.dumps(body_component, indent=6)}")
    
    # Simulate API payload
    components = []
    if body_component:
        components.append(body_component)
    
    template_vals = {
        'name': template['template_name'],
        'language': {'code': template['lang_code']},
    }
    if components:
        template_vals['components'] = components
    
    print(f"\n📡 API Payload:")
    print(f"   {json.dumps(template_vals, indent=6)}")
    
    # Analyze result
    if body_component and body_component.get('parameters'):
        actual_text = body_component['parameters'][0]['text']
        print(f"\n📱 WhatsApp Message Content: '{actual_text}'")
        
        if template['body'] == actual_text:
            print("   ✅ CORRECT: Full template body sent")
            return True
        else:
            print(f"   ⚠️  PARTIAL: Expected '{template['body']}', got '{actual_text}'")
            return False
    else:
        print(f"\n📱 WhatsApp Message Content: (empty or missing)")
        print("   ❌ BROKEN: No body content sent")
        return False

def test_template_scenarios():
    """Test different template scenarios"""
    print("🧪 Template Body Component Fix Tests")
    print("=" * 60)
    
    # Test templates
    template_no_vars = mock_template_without_variables()
    template_with_vars = mock_template_with_variables()
    
    results = []
    
    # Test 1: Template without variables - OLD implementation
    print("\n" + "="*60)
    print("TEST 1: Template WITHOUT variables - OLD (broken) implementation")
    result1 = simulate_template_sending(template_no_vars, "OLD Implementation", old_get_body_component)
    results.append(("Template without vars (OLD)", result1))
    
    # Test 2: Template without variables - NEW implementation  
    print("\n" + "="*60)
    print("TEST 2: Template WITHOUT variables - NEW (fixed) implementation")
    result2 = simulate_template_sending(template_no_vars, "NEW Implementation", new_get_body_component)
    results.append(("Template without vars (NEW)", result2))
    
    # Test 3: Template with variables - OLD implementation
    print("\n" + "="*60)
    print("TEST 3: Template WITH variables - OLD implementation")
    result3 = simulate_template_sending(template_with_vars, "OLD Implementation", old_get_body_component)
    results.append(("Template with vars (OLD)", result3))
    
    # Test 4: Template with variables - NEW implementation
    print("\n" + "="*60)
    print("TEST 4: Template WITH variables - NEW implementation")
    result4 = simulate_template_sending(template_with_vars, "NEW Implementation", new_get_body_component)
    results.append(("Template with vars (NEW)", result4))
    
    return results

def analyze_api_payload_difference():
    """Analyze the difference in API payloads"""
    print("\n" + "="*60)
    print("📊 API PAYLOAD ANALYSIS")
    print("=" * 60)
    
    template = mock_template_without_variables()
    
    # OLD implementation
    old_body = old_get_body_component(template, {}, {})
    old_components = [old_body] if old_body else []
    old_payload = {
        'name': template['template_name'],
        'language': {'code': template['lang_code']},
    }
    if old_components:
        old_payload['components'] = old_components
    
    # NEW implementation
    new_body = new_get_body_component(template, {}, {})
    new_components = [new_body] if new_body else []
    new_payload = {
        'name': template['template_name'],
        'language': {'code': template['lang_code']},
    }
    if new_components:
        new_payload['components'] = new_components
    
    print("🔴 OLD (Broken) API Payload:")
    print(json.dumps(old_payload, indent=2))
    print("\n🟢 NEW (Fixed) API Payload:")
    print(json.dumps(new_payload, indent=2))
    
    print("\n📋 Key Differences:")
    if 'components' not in old_payload:
        print("   ❌ OLD: Missing 'components' field entirely")
    else:
        print("   ⚠️  OLD: Empty or minimal components")
    
    if 'components' in new_payload and new_payload['components']:
        print("   ✅ NEW: Includes body component with actual template text")
        if new_payload['components'][0]['parameters']:
            actual_text = new_payload['components'][0]['parameters'][0]['text']
            print(f"   ✅ NEW: Body text = '{actual_text}'")
    
    print("\n🎯 Why OLD implementation caused 'x' character:")
    print("   1. Template 'hello' has no variables ({{1}}, {{2}}, etc.)")
    print("   2. OLD code: if not self.variable_ids: return None")
    print("   3. Result: No body component sent to Taqnyat API")
    print("   4. Taqnyat API: Receives template request without body content")
    print("   5. WhatsApp: Shows placeholder 'x' instead of actual message")
    
    print("\n🎯 How NEW implementation fixes it:")
    print("   1. Check for body variables specifically (not all variables)")
    print("   2. If no body variables: send static body text as parameter")
    print("   3. Result: Body component with actual 'hello' text sent")
    print("   4. Taqnyat API: Receives complete template with body content")
    print("   5. WhatsApp: Shows full message 'hello' to recipient")

def main():
    """Run all tests and analysis"""
    print("🔧 WhatsApp Template Body Component Fix")
    print("Fixing the 'x' character issue for templates without variables")
    print("=" * 70)
    
    # Run template tests
    results = test_template_scenarios()
    
    # Analyze API differences
    analyze_api_payload_difference()
    
    # Summary
    print("\n" + "="*70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed >= 2:  # At least the NEW implementations should pass
        print("\n🎉 Fix is working correctly!")
        print("✅ Templates without variables now send full body content")
        print("✅ Templates with variables continue to work as before")
        print("✅ Recipients will receive 'hello' instead of 'x'")
    else:
        print("\n⚠️  Fix needs review - some tests failed")
    
    print("\n🚀 Next Steps:")
    print("1. Deploy the fixed _get_body_component() method")
    print("2. Test sending the 'test [odoo3]' template")
    print("3. Verify recipient receives 'hello' message")
    print("4. Confirm no regression for templates with variables")

if __name__ == "__main__":
    main()
