# WhatsApp Module Indexing System - Usage Guide

## 🎯 Overview

The WhatsApp module now includes comprehensive indexing improvements for enhanced performance, monitoring, and scalability. This guide explains how to use and benefit from these improvements.

## 🚀 Quick Start

### 1. Installation

The indexing improvements are automatically applied when you install or upgrade the WhatsApp module:

```bash
# Upgrade the module
odoo-bin -u whatsapp -d your_database
```

### 2. Verification

To verify that indexing is working correctly, use the built-in test system:

```python
# From Odoo shell
from odoo.addons.whatsapp.tools.test_indexing import test_indexing_system, print_test_results

# Run comprehensive tests
results = test_indexing_system(env)
print_test_results(results)
```

## 📊 Performance Monitoring

### 1. Access Performance Dashboard

Navigate to: **WhatsApp > Performance > Message Performance**

Features:
- Real-time message performance metrics
- Send and delivery time tracking
- Success/failure rate analysis
- Account performance comparison

### 2. Account Performance Monitoring

Navigate to: **WhatsApp > Performance > Account Performance**

Features:
- API usage tracking
- Connection health monitoring
- Template sync status
- Performance alerts

### 3. Performance Metrics

The system tracks:
- **Send Duration**: Time taken to send messages
- **Delivery Duration**: Time from sent to delivered
- **API Call Count**: Daily API usage per account
- **Connection Health**: Last successful connection test
- **Sync Status**: Template synchronization timestamps

## 🔍 Enhanced Search

### 1. Account Search

The system now provides enhanced search capabilities:

```python
# Search by account name, token, or provider
accounts = env['whatsapp.account'].search([
    ('search_keywords', 'ilike', 'taqnyat')
])

# Search by partial token
accounts = env['whatsapp.account'].search([
    ('search_keywords', 'ilike', 'token_prefix')
])
```

### 2. Message Search

Optimized message searches:

```python
# Find messages by UID (webhook processing)
message = env['whatsapp.message'].search([
    ('msg_uid', '=', 'wamid.message_id')
])

# Find messages by account and state
messages = env['whatsapp.message'].search([
    ('wa_account_id', '=', account_id),
    ('state', '=', 'delivered')
])
```

## 🛠️ API Usage Monitoring

### 1. Track API Calls

API calls are automatically tracked:

```python
# Check current API usage
account = env['whatsapp.account'].browse(account_id)
print(f"API calls today: {account.api_call_count}")
print(f"Last reset: {account.last_api_reset}")
```

### 2. Rate Limiting Foundation

The system provides foundation for rate limiting:

```python
# Check if account is approaching limits
if account.api_call_count > 100:
    # Implement rate limiting logic
    pass
```

### 3. Automatic Reset

API counters are automatically reset daily via cron job.

## 📈 Performance Analysis

### 1. Identify Slow Operations

Use performance filters to identify issues:

- **Slow Send (>2s)**: Messages taking longer than 2 seconds to send
- **Very Slow Send (>5s)**: Messages taking longer than 5 seconds
- **Slow Delivery (>10s)**: Messages taking longer than 10 seconds to deliver
- **Very Slow Delivery (>30s)**: Messages taking longer than 30 seconds

### 2. Performance Trends

Analyze performance over time:

```python
# Get performance statistics
env.cr.execute("""
    SELECT 
        DATE(create_date) as date,
        AVG(send_duration) as avg_send_time,
        AVG(delivery_duration) as avg_delivery_time,
        COUNT(*) as message_count
    FROM whatsapp_message 
    WHERE create_date >= NOW() - INTERVAL '30 days'
    AND send_duration IS NOT NULL
    GROUP BY DATE(create_date)
    ORDER BY date DESC
""")
results = env.cr.fetchall()
```

## 🔧 Maintenance

### 1. Automatic Cleanup

The system automatically cleans up old performance data:

- **Frequency**: Weekly
- **Retention**: 90 days
- **Action**: Removes performance tracking fields from old messages

### 2. Manual Maintenance

Force cleanup if needed:

```python
# Manual cleanup of old performance data
from datetime import datetime, timedelta

cutoff_date = datetime.now() - timedelta(days=90)
old_messages = env['whatsapp.message'].search([
    ('create_date', '<', cutoff_date),
    ('state', 'in', ['delivered', 'read']),
    ('send_duration', '!=', False)
])

# Remove performance tracking fields
old_messages.write({
    'send_duration': False,
    'delivery_duration': False
})
```

### 3. Index Maintenance

Check index usage:

```sql
-- Check index usage statistics
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE indexname LIKE 'whatsapp_%'
ORDER BY idx_scan DESC;
```

## 🚨 Troubleshooting

### 1. Performance Issues

If you notice performance issues:

1. Check the performance dashboard for bottlenecks
2. Review API usage patterns
3. Verify database indexes are in place
4. Check system resources

### 2. Missing Indexes

To manually apply indexes:

```sql
-- Connect to PostgreSQL and run:
\i /path/to/custom_addons/whatsapp/data/whatsapp_indexes.sql
```

### 3. Test System Health

Run comprehensive tests:

```python
from odoo.addons.whatsapp.tools.test_indexing import *

# Test specific components
test_database_indexes(env)
test_performance_tracking(env)
test_search_optimization(env)
test_cron_jobs(env)
test_performance_views(env)
```

## 📋 Best Practices

### 1. Regular Monitoring

- Check performance dashboard weekly
- Review API usage patterns monthly
- Analyze trends quarterly

### 2. Optimization

- Use performance filters to identify slow operations
- Monitor account performance regularly
- Keep templates synchronized

### 3. Scaling

- Monitor API usage as volume increases
- Consider multiple accounts for load distribution
- Use performance metrics for capacity planning

## 🔗 Integration

### 1. Custom Reports

Create custom performance reports:

```python
# Custom performance report
def generate_performance_report(env, date_from, date_to):
    return env['whatsapp.message'].read_group([
        ('create_date', '>=', date_from),
        ('create_date', '<=', date_to),
    ], ['wa_account_id', 'state', 'send_duration:avg', 'delivery_duration:avg'], 
       ['wa_account_id', 'state'])
```

### 2. External Monitoring

Integrate with external monitoring systems:

```python
# Export metrics for external monitoring
def export_metrics(env):
    accounts = env['whatsapp.account'].search([('active', '=', True)])
    metrics = []
    
    for account in accounts:
        metrics.append({
            'account_id': account.id,
            'account_name': account.name,
            'api_calls_today': account.api_call_count,
            'last_connection_test': account.last_connection_test,
            'last_sync_date': account.last_sync_date,
        })
    
    return metrics
```

## 📞 Support

For questions or issues:

1. Check the performance dashboard for insights
2. Review this usage guide
3. Run the test system to identify issues
4. Contact the development team for advanced troubleshooting

---

**Note**: All indexing improvements are backward compatible and optional. Existing functionality remains unchanged.
