<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- System parameter to control global WhatsApp phone field integration -->
        <record id="whatsapp_global_phone_integration_param" model="ir.config_parameter">
            <field name="key">whatsapp.global_phone_integration</field>
            <field name="value">True</field>
        </record>
        
        <!-- System parameter to control WhatsApp button cache duration (in milliseconds) -->
        <record id="whatsapp_availability_cache_duration_param" model="ir.config_parameter">
            <field name="key">whatsapp.availability_cache_duration</field>
            <field name="value">300000</field> <!-- 5 minutes -->
        </record>
        
        <!-- System parameter to control which models should exclude WhatsApp buttons -->
        <record id="whatsapp_excluded_models_param" model="ir.config_parameter">
            <field name="key">whatsapp.excluded_models</field>
            <field name="value"></field> <!-- Comma-separated list of model names to exclude -->
        </record>
    </data>
</odoo>
