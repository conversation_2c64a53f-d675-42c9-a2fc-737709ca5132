-- WhatsApp Module Database Indexes for Performance Optimization
-- These indexes improve query performance for frequently accessed data

-- Index for WhatsApp Account searches by token (partial)
CREATE INDEX IF NOT EXISTS whatsapp_account_token_partial_idx 
ON whatsapp_account (substring(token, 1, 10)) 
WHER<PERSON> token IS NOT NULL;

-- Index for WhatsApp Account active status and company filtering
CREATE INDEX IF NOT EXISTS whatsapp_account_active_company_idx 
ON whatsapp_account (active, id) 
WHERE active = true;

-- Index for WhatsApp Account search keywords (for enhanced search)
CREATE INDEX IF NOT EXISTS whatsapp_account_search_keywords_idx 
ON whatsapp_account USING gin(to_tsvector('english', search_keywords))
WHERE search_keywords IS NOT NULL;

-- Index for WhatsApp Account API monitoring
CREATE INDEX IF NOT EXISTS whatsapp_account_api_monitoring_idx 
ON whatsapp_account (last_api_reset, api_call_count);

-- Index for WhatsApp Account sync status
CREATE INDEX IF NOT EXISTS whatsapp_account_sync_status_idx 
ON whatsapp_account (last_sync_date DESC, last_connection_test DESC);

-- Index for WhatsApp Messages by account and state
CREATE INDEX IF NOT EXISTS whatsapp_message_account_state_idx 
ON whatsapp_message (wa_account_id, state, create_date DESC);

-- Index for WhatsApp Messages by mobile number and account (for conversation lookup)
CREATE INDEX IF NOT EXISTS whatsapp_message_mobile_account_idx 
ON whatsapp_message (mobile_number_formatted, wa_account_id, create_date DESC);

-- Index for WhatsApp Messages by message UID (for webhook processing)
CREATE INDEX IF NOT EXISTS whatsapp_message_msg_uid_idx 
ON whatsapp_message (msg_uid) 
WHERE msg_uid IS NOT NULL;

-- Index for WhatsApp Messages by template (for template usage analytics)
CREATE INDEX IF NOT EXISTS whatsapp_message_template_idx 
ON whatsapp_message (wa_template_id, create_date DESC) 
WHERE wa_template_id IS NOT NULL;

-- Index for WhatsApp Templates by account and status
CREATE INDEX IF NOT EXISTS whatsapp_template_account_status_idx 
ON whatsapp_template (wa_account_id, status, template_name);

-- Index for WhatsApp Templates by template UID (for sync operations)
CREATE INDEX IF NOT EXISTS whatsapp_template_uid_idx 
ON whatsapp_template (wa_template_uid) 
WHERE wa_template_uid IS NOT NULL;

-- Index for WhatsApp Templates by name and language (for template lookup)
CREATE INDEX IF NOT EXISTS whatsapp_template_name_lang_idx 
ON whatsapp_template (template_name, lang_code, wa_account_id);

-- Index for WhatsApp Template Variables (for template rendering)
CREATE INDEX IF NOT EXISTS whatsapp_template_variable_template_idx 
ON whatsapp_template_variable (wa_template_id, line_type, field_order);

-- Index for WhatsApp Template Buttons (for interactive templates)
CREATE INDEX IF NOT EXISTS whatsapp_template_button_template_idx 
ON whatsapp_template_button (wa_template_id, button_type, sequence);

-- Index for Discuss Channels with WhatsApp integration
CREATE INDEX IF NOT EXISTS discuss_channel_whatsapp_idx 
ON discuss_channel (whatsapp_number, wa_account_id) 
WHERE whatsapp_number IS NOT NULL;

-- Index for Mail Messages with WhatsApp context
CREATE INDEX IF NOT EXISTS mail_message_whatsapp_idx 
ON mail_message (whatsapp_inbound_msg_uid) 
WHERE whatsapp_inbound_msg_uid IS NOT NULL;

-- Composite index for WhatsApp message delivery tracking
CREATE INDEX IF NOT EXISTS whatsapp_message_delivery_tracking_idx 
ON whatsapp_message (wa_account_id, state, mobile_number_formatted, create_date DESC)
WHERE state IN ('sent', 'delivered', 'read', 'error');

-- Index for WhatsApp account notification users (Many2many relation)
CREATE INDEX IF NOT EXISTS whatsapp_account_notify_users_account_idx 
ON whatsapp_account_res_users_rel (whatsapp_account_id);

CREATE INDEX IF NOT EXISTS whatsapp_account_notify_users_user_idx 
ON whatsapp_account_res_users_rel (res_users_id);

-- Index for WhatsApp account allowed companies (Many2many relation)
CREATE INDEX IF NOT EXISTS whatsapp_account_companies_account_idx 
ON whatsapp_account_res_company_rel (whatsapp_account_id);

CREATE INDEX IF NOT EXISTS whatsapp_account_companies_company_idx 
ON whatsapp_account_res_company_rel (res_company_id);

-- Performance monitoring view for WhatsApp operations
CREATE OR REPLACE VIEW whatsapp_performance_stats AS
SELECT 
    wa.id as account_id,
    wa.name as account_name,
    wa.api_call_count,
    wa.last_api_reset,
    wa.last_sync_date,
    wa.last_connection_test,
    COUNT(wm.id) as total_messages,
    COUNT(CASE WHEN wm.state = 'sent' THEN 1 END) as sent_messages,
    COUNT(CASE WHEN wm.state = 'delivered' THEN 1 END) as delivered_messages,
    COUNT(CASE WHEN wm.state = 'error' THEN 1 END) as error_messages,
    COUNT(wt.id) as total_templates,
    COUNT(CASE WHEN wt.status = 'approved' THEN 1 END) as approved_templates
FROM whatsapp_account wa
LEFT JOIN whatsapp_message wm ON wa.id = wm.wa_account_id
LEFT JOIN whatsapp_template wt ON wa.id = wt.wa_account_id
WHERE wa.active = true
GROUP BY wa.id, wa.name, wa.api_call_count, wa.last_api_reset, wa.last_sync_date, wa.last_connection_test;

-- Index for the performance stats view
CREATE INDEX IF NOT EXISTS whatsapp_performance_stats_account_idx 
ON whatsapp_account (id, name, api_call_count, last_api_reset, last_sync_date, last_connection_test)
WHERE active = true;
