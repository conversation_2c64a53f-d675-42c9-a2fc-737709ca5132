<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Cron job to reset daily API counters -->
    <record id="ir_cron_reset_whatsapp_api_counters" model="ir.cron">
        <field name="name">Reset WhatsApp API Counters</field>
        <field name="model_id" ref="whatsapp.model_whatsapp_account"/>
        <field name="state">code</field>
        <field name="code">model._reset_daily_api_counter()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="doall" eval="False"/>
        <field name="active" eval="True"/>
    </record>

    <!-- Cron job to clean up old performance data -->
    <record id="ir_cron_cleanup_whatsapp_performance" model="ir.cron">
        <field name="name">Cleanup WhatsApp Performance Data</field>
        <field name="model_id" ref="whatsapp.model_whatsapp_message"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
# Clean up old performance data (keep last 90 days)
from datetime import datetime, timedelta
cutoff_date = datetime.now() - timedelta(days=90)
old_messages = model.search([
    ('create_date', '<', cutoff_date),
    ('state', 'in', ['delivered', 'read']),
    ('send_duration', '!=', False)
])
# Only remove performance tracking fields, keep the message records
old_messages.write({
    'send_duration': False,
    'delivery_duration': False
})
        ]]></field>
        <field name="interval_number">1</field>
        <field name="interval_type">weeks</field>
        <field name="doall" eval="False"/>
        <field name="active" eval="True"/>
    </record>
</odoo>
