# Part of Odoo. See LICENSE file for full copyright and licensing details.

import hashlib
import hmac
import json
import logging
from markupsafe import Markup
from werkzeug.exceptions import Forbidden
from datetime import datetime

from http import HTTPStatus
from odoo import http, _
from odoo.http import request
from odoo.tools import consteq
from odoo.addons.whatsapp.tools.session_manager import update_session_on_message
from odoo.addons.whatsapp.tools.taqnyat_config import MESSAGE_STATUSES, WEBHOOK_EVENTS

_logger = logging.getLogger(__name__)


class Webhook(http.Controller):

    @http.route('/whatsapp/webhook/', methods=['POST'], type="json", auth="public")
    def webhookpost(self):
        data = json.loads(request.httprequest.data)

        # Taqnyat.sa webhook structure is different from Meta's
        # Check if it's a Taqnyat.sa webhook format
        if data.get('type') == 'whatsapp':
            # Taqnyat.sa webhook format
            self._process_taqnyat_webhook(data)
        else:
            # Legacy Meta webhook format (for backward compatibility)
            self._process_meta_webhook(data)

    def _process_taqnyat_webhook(self, data):
        """Process Taqnyat.sa webhook format with enhanced handling"""
        try:
            # Enhanced account identification
            wa_account_id = self._identify_taqnyat_account(data)
            if not wa_account_id:
                _logger.warning("No WhatsApp account found for Taqnyat webhook: %s", data)
                return {'status': 'error', 'message': 'Account not found'}

            _logger.info("Processing Taqnyat webhook for account %s [%s]",
                        wa_account_id.name, wa_account_id.id)

            # Process delivery reports/status updates
            if data.get('statuses'):
                for status in data['statuses']:
                    self._process_taqnyat_status_update(status, wa_account_id)

            # Process inbound messages
            if data.get('notifications'):
                for notification in data['notifications']:
                    self._process_taqnyat_inbound_message(notification, wa_account_id)

            # Process template status updates
            if data.get('template_status'):
                self._process_taqnyat_template_status(data['template_status'], wa_account_id)

            return {'status': 'success', 'processed': True}

        except Exception as e:
            _logger.error("Error processing Taqnyat webhook: %s", str(e), exc_info=True)
            return {'status': 'error', 'message': str(e)}

    def _identify_taqnyat_account(self, data):
        """
        Identify WhatsApp account from webhook data
        Enhanced logic to match account based on available data
        """
        # Try to identify by phone number in the data
        phone_number = None
        if data.get('notifications'):
            phone_number = data['notifications'][0].get('to')
        elif data.get('statuses'):
            phone_number = data['statuses'][0].get('recipient_id')

        if phone_number:
            # Look for account with matching phone number configuration
            account = request.env['whatsapp.account'].sudo().search([
                ('active', '=', True),
                '|',
                ('phone_uid', '=', phone_number),
                ('name', 'ilike', phone_number)
            ], limit=1)
            if account:
                return account

        # Fallback to first active account
        return request.env['whatsapp.account'].sudo().search([('active', '=', True)], limit=1)

    def _process_taqnyat_status_update(self, status_data, wa_account_id):
        """
        Process message status updates from Taqnyat webhook
        """
        message_id = status_data.get('message_id')
        status = status_data.get('status', '').lower()
        recipient_id = status_data.get('recipient_id')
        timestamp = status_data.get('timestamp')

        if not message_id:
            _logger.warning("Status update without message_id: %s", status_data)
            return

        _logger.info("Processing status update: message_id=%s, status=%s, recipient=%s",
                    message_id, status, recipient_id)

        # Find the message in our system
        message = request.env['whatsapp.message'].sudo().search([
            ('msg_uid', '=', message_id),
            ('wa_account_id', '=', wa_account_id.id)
        ], limit=1)

        if message:
            # Update message status
            status_mapping = {
                'queued': 'outbound',
                'dispatched': 'sent',
                'sent': 'sent',
                'delivered': 'delivered',
                'read': 'read',
                'failed': 'error'
            }

            new_state = status_mapping.get(status, 'sent')
            message.write({
                'state': new_state,
                'failure_reason': status_data.get('error_message') if status == 'failed' else False
            })

            # Update session tracking for delivered messages
            if status in ['delivered', 'read'] and message.mobile_number:
                update_session_on_message(
                    request.env, message.mobile_number, wa_account_id.id,
                    message_id, 'outbound'
                )
        else:
            _logger.warning("Message not found for status update: %s", message_id)

    def _process_taqnyat_inbound_message(self, notification_data, wa_account_id):
        """
        Process inbound messages from Taqnyat webhook
        """
        try:
            message_data = notification_data.get('message', {})
            from_number = notification_data.get('from')
            message_id = message_data.get('id')
            message_type = message_data.get('type', 'text')
            timestamp = notification_data.get('timestamp')

            if not from_number or not message_id:
                _logger.warning("Incomplete inbound message data: %s", notification_data)
                return

            _logger.info("Processing inbound message: from=%s, type=%s, id=%s",
                        from_number, message_type, message_id)

            # Extract message content based on type
            content = self._extract_message_content(message_data, message_type)

            # Create or update contact
            partner = self._get_or_create_partner(from_number, wa_account_id)

            # Create message record
            message_vals = {
                'wa_account_id': wa_account_id.id,
                'partner_id': partner.id if partner else False,
                'mobile_number': from_number,
                'msg_uid': message_id,
                'message_type': 'inbound',
                'state': 'received',
                'body': content.get('text', ''),
                'wa_message_id': message_id
            }

            # Add media information if present
            if message_type in ['image', 'video', 'audio', 'document']:
                media_info = message_data.get(message_type, {})
                message_vals.update({
                    'attachment_ids': self._process_inbound_media(media_info, wa_account_id)
                })

            message = request.env['whatsapp.message'].sudo().create(message_vals)

            # Update session tracking
            update_session_on_message(
                request.env, from_number, wa_account_id.id, message_id, 'inbound'
            )

            # Trigger any automated responses or notifications
            self._trigger_inbound_message_actions(message, wa_account_id)

        except Exception as e:
            _logger.error("Error processing inbound message: %s", str(e), exc_info=True)

    def _extract_message_content(self, message_data, message_type):
        """
        Extract content from message data based on type
        """
        content = {'text': '', 'media_id': None, 'caption': None}

        if message_type == 'text':
            content['text'] = message_data.get('text', {}).get('body', '')

        elif message_type in ['image', 'video', 'audio', 'document']:
            media_data = message_data.get(message_type, {})
            content['media_id'] = media_data.get('id')
            content['caption'] = media_data.get('caption', '')
            content['text'] = content['caption']

        elif message_type == 'location':
            location_data = message_data.get('location', {})
            lat = location_data.get('latitude')
            lng = location_data.get('longitude')
            name = location_data.get('name', '')
            address = location_data.get('address', '')
            content['text'] = f"Location: {name} {address} ({lat}, {lng})"

        elif message_type == 'contacts':
            contacts = message_data.get('contacts', [])
            contact_names = [c.get('name', {}).get('formatted_name', 'Unknown') for c in contacts]
            content['text'] = f"Contacts: {', '.join(contact_names)}"

        elif message_type == 'interactive':
            interactive_data = message_data.get('interactive', {})
            if interactive_data.get('type') == 'button_reply':
                button_reply = interactive_data.get('button_reply', {})
                content['text'] = f"Button: {button_reply.get('title', '')}"
            elif interactive_data.get('type') == 'list_reply':
                list_reply = interactive_data.get('list_reply', {})
                content['text'] = f"List: {list_reply.get('title', '')}"

        return content

    def _get_or_create_partner(self, phone_number, wa_account_id):
        """
        Get or create partner for the phone number
        """
        # Clean phone number
        clean_phone = phone_number
        if clean_phone.startswith('+'):
            clean_phone = clean_phone[1:]
        elif clean_phone.startswith('00'):
            clean_phone = clean_phone[2:]

        # Search for existing partner
        partner = request.env['res.partner'].sudo().search([
            '|',
            ('mobile', '=', phone_number),
            ('mobile', '=', clean_phone),
            '|',
            ('phone', '=', phone_number),
            ('phone', '=', clean_phone)
        ], limit=1)

        if not partner:
            # Create new partner
            partner = request.env['res.partner'].sudo().create({
                'name': f"WhatsApp Contact {clean_phone}",
                'mobile': clean_phone,
                'is_company': False,
                'supplier_rank': 0,
                'customer_rank': 1
            })

        return partner

    def _process_inbound_media(self, media_info, wa_account_id):
        """
        Process inbound media and create attachments
        """
        media_id = media_info.get('id')
        if not media_id:
            return []

        try:
            # Download media using API
            from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
            api = WhatsAppApi(wa_account_id)
            media_content = api._get_whatsapp_document(media_id)

            # Create attachment
            attachment = request.env['ir.attachment'].sudo().create({
                'name': media_info.get('filename', f'media_{media_id}'),
                'datas': media_content,
                'mimetype': media_info.get('mime_type', 'application/octet-stream'),
                'res_model': 'whatsapp.message',
                'type': 'binary'
            })

            return [(4, attachment.id)]

        except Exception as e:
            _logger.error("Error processing inbound media %s: %s", media_id, str(e))
            return []

    def _trigger_inbound_message_actions(self, message, wa_account_id):
        """
        Trigger automated actions for inbound messages
        """
        try:
            # Notify configured users
            if wa_account_id.notify_user_ids:
                # Create notification or send email
                subject = f"New WhatsApp message from {message.mobile_number}"
                body = f"Message: {message.body[:100]}..."

                for user in wa_account_id.notify_user_ids:
                    user.notify_info(message=body, title=subject)

            # Check for automated responses (can be extended)
            self._check_automated_responses(message, wa_account_id)

        except Exception as e:
            _logger.error("Error triggering inbound message actions: %s", str(e))

    def _check_automated_responses(self, message, wa_account_id):
        """
        Check and trigger automated responses
        """
        # This can be extended to implement automated responses
        # based on keywords, business hours, etc.
        pass

    def _process_taqnyat_template_status(self, template_status_data, wa_account_id):
        """
        Process template status updates from Taqnyat webhook
        """
        template_id = template_status_data.get('template_id')
        status = template_status_data.get('status', '').lower()

        if not template_id:
            return

        # Find template in our system
        template = request.env['whatsapp.template'].sudo().search([
            ('wa_template_uid', '=', template_id),
            ('wa_account_id', '=', wa_account_id.id)
        ], limit=1)

        if template:
            template.write({'status': status})

            if status == 'rejected':
                reason = template_status_data.get('reason', 'No reason provided')
                template.message_post(
                    body=f"Template rejected: {reason}",
                    message_type='comment'
                )
        else:
            _logger.warning("Template not found for status update: %s", template_id)

    def _process_meta_webhook(self, data):
        """Process legacy Meta webhook format for backward compatibility"""
        for entry in data.get('entry', []):
            account_id = entry['id']
            account = request.env['whatsapp.account'].sudo().search(
                [('account_uid', '=', account_id)])
            if account and not self._check_signature(account):
                raise Forbidden()

            for changes in entry.get('changes', []):
                value = changes['value']
                phone_number_id = value.get('metadata', {}).get('phone_number_id', {})
                if not phone_number_id:
                    phone_number_id = value.get('whatsapp_business_api_data', {}).get('phone_number_id', {})
                if phone_number_id:
                    wa_account_id = request.env['whatsapp.account'].sudo().search([
                        ('phone_uid', '=', phone_number_id), ('account_uid', '=', account_id)])
                    if wa_account_id:
                        # Process Messages and Status webhooks
                        if changes['field'] == 'messages':
                            request.env['whatsapp.message']._process_statuses(value)
                            wa_account_id._process_messages(value)
                    else:
                        _logger.warning("There is no phone configured for this whatsapp webhook : %s ", data)

                # Process Template webhooks
                if value.get('message_template_id'):
                    # There is no user in webhook, so we need to SUPERUSER_ID to write on template object
                    template = request.env['whatsapp.template'].sudo().with_context(active_test=False).search([('wa_template_uid', '=', value['message_template_id'])])
                    if template:
                        if changes['field'] == 'message_template_status_update':
                            template.write({'status': value['event'].lower()})
                            if value['event'].lower() == 'rejected':
                                body = _("Your Template has been rejected.")
                                description = value.get('other_info', {}).get('description') or value.get('reason')
                                if description:
                                    body += Markup("<br/>") + _("Reason : %s", description)
                                template.message_post(body=body)
                            continue
                        if changes['field'] == 'message_template_quality_update':
                            new_quality_score = value['new_quality_score'].lower()
                            new_quality_score = {'unknown': 'none'}.get(new_quality_score, new_quality_score)
                            template.write({'quality': new_quality_score})
                            continue
                        if changes['field'] == 'template_category_update':
                            template.write({'template_type': value['new_category'].lower()})
                            continue
                        _logger.warning("Unknown Template webhook : %s ", value)
                    else:
                        _logger.warning("No Template found for this webhook : %s ", value)

    @http.route('/whatsapp/webhook/', methods=['GET'], type="http", auth="public", csrf=False)
    def webhookget(self, **kwargs):
        """
            This controller is used to verify the webhook.
            if challenge is matched then it will make response with challenge.
            once it is verified the webhook will be activated.
        """
        token = kwargs.get('hub.verify_token')
        mode = kwargs.get('hub.mode')
        challenge = kwargs.get('hub.challenge')
        if not (token and mode and challenge):
            return Forbidden()
        wa_account = request.env['whatsapp.account'].sudo().search([('webhook_verify_token', '=', token)])
        if mode == 'subscribe' and wa_account:
            response = request.make_response(challenge)
            response.status_code = HTTPStatus.OK
            return response
        response = request.make_response({})
        response.status_code = HTTPStatus.FORBIDDEN
        return response

    def _check_signature(self, business_account):
        """Check webhook signature - Taqnyat.sa may not use the same signature verification as Meta"""
        # For Taqnyat.sa, signature verification might be different or not required
        # Check if this is a Meta webhook (has app_secret)
        if business_account and business_account.app_secret:
            # Meta webhook signature verification
            signature = request.httprequest.headers.get('X-Hub-Signature-256')
            if not signature or not signature.startswith('sha256=') or len(signature) != 71:
                # Signature must be valid SHA-256 (sha256=<64 hex digits>)
                _logger.warning('Invalid signature header %r', signature)
                return False

            expected = hmac.new(
                business_account.app_secret.encode(),
                msg=request.httprequest.data,
                digestmod=hashlib.sha256,
            ).hexdigest()

            return consteq(signature[7:], expected)
        else:
            # For Taqnyat.sa webhooks, we might not have signature verification
            # or it might use a different method
            # For now, we'll allow it (you may want to implement Taqnyat-specific verification)
            return True

    @http.route('/taqnyat/webhook', type='json', auth='public', methods=['POST'], csrf=False)
    def taqnyat_webhook_handler(self):
        """
        Dedicated Taqnyat WhatsApp webhook handler

        Handles incoming webhooks from Taqnyat WhatsApp Business API
        Webhook URL: https://your-domain.com/taqnyat/webhook
        """
        try:
            data = request.jsonrequest
            _logger.info("Taqnyat webhook received: %s", json.dumps(data, indent=2))

            # Handle verification handshake
            pass_phrase = data.get('passPhrase')
            if pass_phrase:
                _logger.info("Taqnyat webhook passphrase verification")
                return {"status": "verified", "passPhrase": pass_phrase}

            # Basic message processing
            processed_count = 0
            for entry in data.get('entry', []):
                for change in entry.get('changes', []):
                    if change.get('field') == 'messages':
                        processed_count += 1

            _logger.info("Processed %d messages from Taqnyat webhook", processed_count)
            return {"status": "ok", "processed": processed_count}

        except Exception as e:
            _logger.error("Error processing Taqnyat webhook: %s", str(e), exc_info=True)
            return {"status": "error", "message": str(e)}

    @http.route('/taqnyat/webhook', type='http', auth='public', methods=['GET'], csrf=False)
    def taqnyat_webhook_verification(self, **kwargs):
        """Handle webhook verification for Taqnyat"""
        try:
            verify_token = kwargs.get('hub.verify_token')
            challenge = kwargs.get('hub.challenge')
            mode = kwargs.get('hub.mode')

            if mode == 'subscribe' and verify_token:
                _logger.info("Taqnyat webhook verification successful")
                return challenge
            else:
                _logger.warning("Taqnyat webhook verification failed")
                return "Verification failed", 403

        except Exception as e:
            _logger.error("Error in Taqnyat webhook verification: %s", str(e))
            return "Error", 500
