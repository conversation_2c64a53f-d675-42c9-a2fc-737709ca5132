# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "%(create_count)s were created, %(update_count)s were updated"
msgstr "%(create_count)s가 생성되었고, %(update_count)s가 업데이트 되었습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (사본)"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(template_name)s [%(account_name)s]"
msgstr "%(template_name)s [%(account_name)s]"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "'%(field)s' does not seem to be a valid field path on %(model)s"
msgstr "'%(field)s' 항목은 %(model)s 이동에 유효한 필드 경로가 아닙니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ", ... (%s Others)"
msgstr ", ... (%s 기타)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_kanban
msgid ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"
msgstr "<i class=\"fa fa-whatsapp me-1\" title=\"메시지 수\" aria-label=\"메시지 수\"/>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"
msgstr ""
"<span class=\"o-whatsapp-font-11\">{{위치 이름}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{주소}}</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "<span class=\"o_stat_text\">Chats</span>"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"
msgstr ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid ""
"<strong>Invalid number: </strong>\n"
"                            <span>make sure to set a country on the Contact or to specify the country code.</span>"
msgstr ""
"<strong>잘못된 번호: </strong>\n"
"                            <span>연락처에서 국가를 설정하거나 국가 코드를 지정해야 합니다.</span>"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A new WhatsApp channel is created for this document"
msgstr "이 문서에 대한 새 WhatsApp 채널이 생성됩니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid ""
"A new template was sent on %(record_link)s.<br>Future replies will be "
"transferred to a new chat."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A phone number is required for WhatsApp channels %(channel_names)s"
msgstr "WhatsApp 채널에는 휴대폰 번호가 필요합니다.%(channel_names)s"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__token
msgid "Access Token"
msgstr "사용 권한 토큰"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Accessible to all Users"
msgstr "모든 사용자 액세스 가능"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Account"
msgstr "계정"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__account
msgid "Account Error"
msgstr "계정 오류"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__account_uid
msgid "Account ID"
msgstr "계정 ID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__active
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__active
msgid "Active"
msgstr "활성화"

#. module: whatsapp
#: model:res.groups,name:whatsapp.group_whatsapp_admin
msgid "Administrator"
msgstr "관리자"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__af
msgid "Afrikaans"
msgstr "아프리칸스어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sq
msgid "Albanian"
msgstr "알바니아dj"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "All dynamic urls must have a placeholder."
msgstr "모든 동적 URL에는 자리 표시자가 있어야 합니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Allow Multi"
msgstr "다중 허용"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__allowed_company_ids
msgid "Allowed Company"
msgstr "허용 회사"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Allowed companies"
msgstr "허용 회사"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_uid
msgid "App ID"
msgstr "앱 ID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_secret
msgid "App Secret"
msgstr "앱 비밀번호"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model_id
msgid "Applies to"
msgstr "적용 대상"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__approved
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Approved"
msgstr "결재 완료"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ar
msgid "Arabic"
msgstr "아랍어"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Archived"
msgstr "보관됨"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__attachment_id
msgid "Attachment"
msgstr "첨부 파일"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_attachment_count
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "Attachment mimetype is not supported by WhatsApp: %s."
msgstr "mimetype 첨부파일은 WhatsApp에서 지원되지 않습니다: %s."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__authentication
msgid "Authentication"
msgstr "인증"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__template_type
msgid ""
"Authentication - One-time passwords that your customers use to authenticate a transaction or login.\n"
"Marketing - Promotions or information about your business, products or services. Or any message that isn't utility or authentication.\n"
"Utility - Messages about a specific transaction, account, order or customer request."
msgstr ""
"인증 - 고객이 거래 또는 로그인을 인증하는 데 사용하는 일회용 비밀번호입니다.\n"
"마케팅 - 비즈니스, 제품 또는 서비스에 대한 프로모션 및 정보입니다. 유틸리티나 인증이 아닌 모든 메시지가 해당됩니다.\n"
"유틸리티 - 특정 거래, 계정, 주문, 고객 요청에 대한 메시지입니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__az
msgid "Azerbaijani"
msgstr "아제르바이잔어"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_base
msgid "Base"
msgstr "기준액"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bn
msgid "Bengali"
msgstr "벵골어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__blacklisted
msgid "Blacklisted Phone Number"
msgstr "블랙리스트에 등록된 전화번호"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__body
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__body
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Body"
msgstr "본문"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Body variables should start at 1 and not skip any number, missing %d"
msgstr "본문 변수는 1부터 시작해야 하며 숫자를 건너뛰거나 %d를 누락해서는 안됩니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__bounced
msgid "Bounced"
msgstr "반송됨"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bg
msgid "Bulgarian"
msgstr "불가리아어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__button_id
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__button
msgid "Button"
msgstr "버튼"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__name
msgid "Button Text"
msgstr "버튼 텍스트"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_1
msgid "Button Url 1"
msgstr "버튼 Url 1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_2
msgid "Button Url 2"
msgstr "버튼 Url 2"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_button_unique_name_per_template
msgid "Button names must be unique in a given template"
msgstr "버튼 이름은 해당 템플릿에서 고유해야 합니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Button variables must be linked to a button."
msgstr "버튼 변수는 버튼에 연결되어야 합니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__button_ids
msgid "Buttons"
msgstr "버튼"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Buttons may only contain one placeholder."
msgstr "버튼은 하나의 자리 표시자만 포함할 수 있습니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__call_number
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__phone_number
msgid "Call Number"
msgstr "전화 번호"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__callback_url
msgid "Callback URL"
msgstr "답신 통화 URL"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid ""
"Can't send message as it has been 24 hours since the last message of the "
"User."
msgstr "사용자의 마지막 메시지 이후 24시간이 지났기 때문에 메시지를 보낼 수 없습니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
msgid "Cancel"
msgstr "취소"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Cancel WhatsApp"
msgstr "왓츠앱 취소"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__cancel
msgid "Cancelled"
msgstr "취소됨"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ca
msgid "Catalan"
msgstr "카탈로니아어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_type
msgid "Category"
msgstr "카테고리"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel_member
msgid "Channel Member"
msgstr "채널 회원"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "채널 유형"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"채팅은 두 사람 사이에 비공개로 고유하게 진행됩니다. 그룹은 초대받은 사람들간에 비공개로 진행됩니다. 채널은 자유롭게 가입할 수 있습니다"
" (설정에 따라 다름)."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_cn
msgid "Chinese (CHN)"
msgstr "중국어 (중국)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_hk
msgid "Chinese (HKG)"
msgstr "중국어 (홍콩)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_tw
msgid "Chinese (TAI)"
msgstr "중국어 (대만)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.ir_actions_server_view_form_whatsapp
msgid "Choose a template..."
msgstr "서식을 선택하세요."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Close"
msgstr "닫기"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_configuration_menu
msgid "Configuration"
msgstr "설정"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Configure Meta Accounts"
msgstr "Meta 계정 설정"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "Configure Templates"
msgstr "서식 설정"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Configure Whatsapp Business Account"
msgstr "Whatsapp 비즈니스 계정 설정"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Create Date"
msgstr "작성일자"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Create an Account on the"
msgstr "다음에서 계정 만들기"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Created On"
msgstr "작성일자"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_uid
msgid "Created by"
msgstr "작성자"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_date
msgid "Created on"
msgstr "작성일"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Credentials look good!"
msgstr "인증 정보는 문제 없습니다"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hr
msgid "Croatian"
msgstr "크로아티아어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__cs
msgid "Czech"
msgstr "체코어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__da
msgid "Danish"
msgstr "덴마크어"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Default Users"
msgstr "기본 사용자"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__deleted
msgid "Deleted"
msgstr "삭제됨"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__delivered
msgid "Delivered"
msgstr "배송완료"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Delivered Messages"
msgstr "전송된 메시지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__disabled
msgid "Disabled"
msgstr "비활성화"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Disallow Multi"
msgstr "다중 비허용"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel
msgid "Discussion Channel"
msgstr "메일 및 채팅 채널"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__display_name
msgid "Display Name"
msgstr "표시명"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__document
msgid "Document"
msgstr "문서"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_ids
msgid "Document IDs"
msgstr "문서 ID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_model
msgid "Document Model Name"
msgstr "문서 모델명"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload failed, please retry after sometime."
msgstr "문서 업로드에 실패했습니다. 나중에 다시 시도해 주세요."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload session open failed, please retry after sometime."
msgstr "문서 업로드 세션을 열지 못했습니다. 잠시 후 다시 시도하세요."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__draft
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Draft"
msgstr "초안"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_unique_name_account_template
msgid "Duplicate template is not allowed for one Meta account."
msgstr "하나의 Meta 계정에 중복된 서식은 허용되지 않습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nl
msgid "Dutch"
msgstr "네덜란드어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__dynamic
msgid "Dynamic"
msgstr "동적"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Dynamic button variable name must be the same as its respective button's "
"name"
msgstr "동적 버튼의 변수 이름은 해당 버튼의 이름과 일치해야 합니다."

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_message_unique_msg_uid
msgid "Each whatsapp message should correspond to a single message uuid."
msgstr "각각의 Whatsapp 메시지는 단일 uuid 메시지에 일치해야 합니다."

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_thread
msgid "Email Thread"
msgstr "이메일 스레드"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/phone_field/phone_field.js:0
msgid "Enable WhatsApp"
msgstr "WhatsApp 사용"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en
msgid "English"
msgstr "영어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_gb
msgid "English (UK)"
msgstr "영어 (영국)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_us
msgid "English (US)"
msgstr "영어 (미국)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__error_msg
msgid "Error Message"
msgstr "오류 메시지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__et
msgid "Estonian"
msgstr "에스토니아어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__error
msgid "Failed"
msgstr "불합격"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Failed Messages"
msgstr "실패 메시지"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_reason
msgid "Failure Reason"
msgstr "실패 이유"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_type
msgid "Failure Type"
msgstr "실패 유형"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_name
msgid "Field"
msgstr "필드"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__field
msgid "Field of Model"
msgstr "모델 필드"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Field template variables %(var_names)s must be associated with a field."
msgstr "필드 템플릿 변수 %(var_names)s는 필드에 연결되어야 합니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "File type %(file_type)s not supported for header type %(header_type)s"
msgstr "%(file_type)s 파일 형식은 %(header_type)s 머리글 형식에 지원되지 않습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fil
msgid "Filipino"
msgstr "필리핀어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fi
msgid "Finnish"
msgstr "핀란드어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_follower_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_partner_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__footer_text
msgid "Footer Message"
msgstr "바닥글 메시지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__free_text
msgid "Free Text"
msgstr "무료 텍스트"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_1
msgid "Free Text 1"
msgstr "무료 문자 1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_10
msgid "Free Text 10"
msgstr "무료 문자 10"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_2
msgid "Free Text 2"
msgstr "무료 문자 2"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_3
msgid "Free Text 3"
msgstr "무료 문자 3"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_4
msgid "Free Text 4"
msgstr "무료 문자 4"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_5
msgid "Free Text 5"
msgstr "무료 문자 5"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_6
msgid "Free Text 6"
msgstr "무료 문자 6"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_7
msgid "Free Text 7"
msgstr "무료 문자 7"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_8
msgid "Free Text 8"
msgstr "무료 문자 8"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_9
msgid "Free Text 9"
msgstr "무료 문자 9"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__free_text_json
msgid "Free Text Template Parameters"
msgstr "무료 텍스트 서식 매개변수"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Free Text template variables must have a demo value."
msgstr "무료 텍스트 서식 변수에는 반드시 데모값이 있어야 합니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Free text variable in the header should be {{1}}"
msgstr "머리글의 자유 텍스트 변수는 {{1}} 변수이어야 합니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fr
msgid "French"
msgstr "불어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ka
msgid "Georgian"
msgstr "조지아 (그루지아)어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__de
msgid "German"
msgstr "독일"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__el
msgid "Greek"
msgstr "그리스어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__green
msgid "Green"
msgstr "녹색"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Group By"
msgstr "그룹별"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels and whatsapp."
msgstr "그룹 승인이나 그룹 자동 구독은 채널 및 WhatsApp에서만 지원되는 기능입니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__gu
msgid "Gujarati"
msgstr "구라자트어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_action
msgid "Has Action"
msgstr "활동이 있습니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_invalid_button_number
msgid "Has Invalid Button Number"
msgstr "잘못된 버튼 번호가 있습니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__has_invalid_number
msgid "Has Invalid Number"
msgstr "잘못된 번호"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__has_message
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_message
msgid "Has Message"
msgstr "메시지가 있습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ha
msgid "Hausa"
msgstr "하우사어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__header
msgid "Header"
msgstr "머리글"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__header_text_1
msgid "Header Free Text"
msgstr "머리글 무료 문자"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_type
msgid "Header Type"
msgstr "머리글 유형"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document is required"
msgstr "머리글 문서가 있어야 합니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document or report is required"
msgstr "머리글 문서 또는 보고서가 있어야 합니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__he
msgid "Hebrew"
msgstr "히브리어"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Hello {{1}}, here is your order with the reference {{2}} ..."
msgstr "안녕하세요 {{1}}, 참조 번호 {{2}}가 포함된 주문서입니다 ..."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hi
msgid "Hindi"
msgstr "힌디어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hu
msgid "Hungarian"
msgstr "헝가리어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__id
msgid "ID"
msgstr "ID"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_recoverable
msgid "Identified Error"
msgstr "확인된 오류"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "선택할 경우, 새로운 메시지에 주의를 기울여야 합니다."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 메시지가 잘못 전달될 수 있습니다."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "If checked, the WhatsApp category is open in the discuss sidebar"
msgstr "선택하면, 메일 및 채팅 사이드바에 WhatsApp 카테고리가 열립니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__image
msgid "Image"
msgstr "이미지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__in_appeal
msgid "In Appeal"
msgstr "문의 중"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__outgoing
msgid "In Queue"
msgstr "대기열"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__inbound
msgid "Inbound"
msgstr "인바운드"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__id
msgid "Indonesian"
msgstr "인도네시아어"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "Insert variable"
msgstr "변수 삽입"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__invalid_phone_number_count
msgid "Invalid Phone Number Count"
msgstr "전화번호 개수가 잘못되었습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ga
msgid "Irish"
msgstr "아일랜드어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_button_dynamic
msgid "Is Button Dynamic"
msgstr "동적 버튼입니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_is_follower
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_is_follower
msgid "Is Follower"
msgstr "팔로워입니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_header_free_text
msgid "Is Header Free Text"
msgstr "머리글 무료 문자입니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__batch_mode
msgid "Is Multiple Records"
msgstr "여러 개의 레코드입니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_active
msgid "Is Whatsapp Channel Active"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__it
msgid "Italian"
msgstr "이탈리아어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ja
msgid "Japanese"
msgstr "일본어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kn
msgid "Kannada"
msgstr "칸나다어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kk
msgid "Kazakh"
msgstr "카자흐어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__rw_rw
msgid "Kinyarwanda"
msgstr "키냐르완다어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ko
msgid "Korean"
msgstr "한국어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ky_kg
msgid "Kyrgyz (Kyrgyzstan)"
msgstr "키르기스어 (키르기스스탄)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__lang_code
msgid "Language"
msgstr "언어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lo
msgid "Lao"
msgstr "라오스어"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Month"
msgstr "전 월"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_uid
msgid "Last Updated by"
msgstr "최근 업데이트"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_date
msgid "Last Updated on"
msgstr "최근 업데이트 일자"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__last_wa_mail_message_id
msgid "Last WA Partner Mail Message"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Week"
msgstr "지난 주"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Year"
msgstr "지난 해"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lv
msgid "Latvian"
msgstr "라트비아어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__limit_exceeded
msgid "Limit Exceeded"
msgstr "한도가 초과되었습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lt
msgid "Lithuanian"
msgstr "리투아니아어"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__location
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__location
msgid "Location"
msgstr "위치"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location Latitude and Longitude %(latitude)s / %(longitude)s is not in "
"proper format."
msgstr "위도 및 경도 %(latitude)s / %(longitude)s 위치가 올바른 형식이 아닙니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Location variable should be 'name', 'address', 'latitude' or 'longitude'. "
"Cannot parse '%(placeholder)s'"
msgstr ""
"위치 변수는 '이름', '주소', '위도' 또는 '경도' 여야 합니다. '%(placeholder)s' 항목 구문을 분석할 수 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location variables should only exist when a \"location\" header is selected."
msgstr "위치 변수는 '위치' 머리글이 선택된 경우에만 존재할 수 있습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mk
msgid "Macedonian"
msgstr "마케도니아어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mail_message_id
msgid "Mail Message"
msgstr "메일 메시지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ms
msgid "Malay"
msgstr "말레이어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ml
msgid "Malayalam"
msgstr "말라얄람어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mr
msgid "Marathi"
msgstr "마라티어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__marketing
msgid "Marketing"
msgstr "마케팅"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 1 Call Number button allowed."
msgstr "통화 번호 버튼은 최대 1개까지 허용됩니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 10 buttons allowed."
msgstr "버튼은 최대 10개까지 허용됩니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 2 URL buttons allowed."
msgstr "URL 버튼은 최대 2개까지 허용됩니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Members"
msgstr "회원"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_message
msgid "Message"
msgstr "메시지"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__templates_count
msgid "Message Count"
msgstr "메시지 수"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__preview_whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__preview_whatsapp
msgid "Message Preview"
msgstr "메시지 미리보기"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Message Statistics Of %(template_name)s"
msgstr "%(template_name)s 항목의 메시지 통계입니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__message_type
msgid "Message Type"
msgstr "메시지 유형"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_ids
#: model:ir.ui.menu,name:whatsapp.whatsapp_message_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Messages"
msgstr "메시지"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__messages_count
msgid "Messages Count"
msgstr "메시지 수"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Meta for Developers"
msgstr "개발자용 메타"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number_formatted
msgid "Mobile Number Formatted"
msgstr "휴대폰 번호 형식이 지정되었습니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Model"
msgstr "모델"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__model
msgid "Model Name"
msgstr "모델명"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "Monitor all recent outgoing and incoming messages"
msgstr "최근 발신 및 수신된 모든 메시지 모니터링"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "My Templates"
msgstr "나의 서식"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__name
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Name"
msgstr "이름"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__network
msgid "Network Error"
msgstr "네트워크 에러"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "No Account Configured yet!"
msgstr "아직 계정을 설정하지 않았습니다!"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "No Templates Found!"
msgstr "서식을 찾을 수 없습니다!"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "No WhatsApp Messages found"
msgstr "WhatsApp 메시지를 찾을 수 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "No approved WhatsApp Templates are available for this model."
msgstr "해당 모델에 사용할 수 있는 WhatsApp 서식이 없습니다."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/web/channel_selector_patch.js:0
msgid "No results found"
msgstr "결과를 찾을 수 없습니다"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Non-descript Error"
msgstr "설명되지 않은 오류"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__none
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__none
msgid "None"
msgstr "없음"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nb
msgid "Norwegian"
msgstr "노르웨이어"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Notifications"
msgstr "알림"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__notify_user_ids
msgid "Notify User"
msgstr "사용자 알림"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of errors"
msgstr "오류 수"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text
msgid "Number of free text"
msgstr "무료 문자 수"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text_button
msgid "Number of free text Buttons"
msgstr "무료 문자 개수 버튼"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수입니다."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only 10 free text is allowed in body of template"
msgstr "서식 본문에는 10개만 무료로 문자를 사용할 수 있습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Only dynamic urls may have a placeholder."
msgstr "자리표지자는 보통 동적 URL에만 사용합니다."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid "Only one attachment is allowed for each message"
msgstr "메시지당 첨부 파일은 하나만 허용됩니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only templates using media header types may have header documents"
msgstr "머리글 문서는 보통 서식이 미디어 머리글 유형인 경우에만 사용합니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_unrecoverable
msgid "Other Technical Error"
msgstr "기타 기술적인 오류"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__outbound
msgid "Outbound"
msgstr "해외"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid "Partner created by incoming WhatsApp message."
msgstr "WhatsApp 수신 메시지를 통해 생성된 파트너입니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__paused
msgid "Paused"
msgstr "일시 중지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Pending"
msgstr "보류 중"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending_deletion
msgid "Pending Deletion"
msgstr "삭제 대기 중"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fa
msgid "Persian"
msgstr "페르시아어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__phone
msgid "Phone"
msgstr "전화번호"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__phone_field
msgid "Phone Field"
msgstr "전화 필드"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_number
msgid "Phone Number"
msgstr "전화번호"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__phone_uid
msgid "Phone Number ID"
msgstr "전화번호 아이디"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Phone number Id is wrong."
msgstr "잘못된 전화번호 아이디입니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Pick an Account..."
msgstr "계정 선택..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Pick users to notify..."
msgstr "알림을 보낼 사용자 선택..."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__name
msgid "Placeholder"
msgstr "자리 표시자"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr "전화나 휴대폰 번호 검색 시 3자 이상 입력하세요."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pl
msgid "Polish"
msgstr "폴란드어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__portal_url
msgid "Portal Link"
msgstr "포털 링크"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_br
msgid "Portuguese (BR)"
msgstr "포르투갈어 (BR)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_pt
msgid "Portuguese (POR)"
msgstr "포르투갈어 (POR)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Preview"
msgstr "미리보기"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Preview WhatsApp"
msgstr "WhatsApp 미리보기"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_preview
msgid "Preview template"
msgstr "서식 미리보기"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pa
msgid "Punjabi"
msgstr "펀자브어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__quality
msgid "Quality"
msgstr "품질 관리"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__quick_reply
msgid "Quick Reply"
msgstr "빠른 답장"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__rating_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__read
msgid "Read"
msgstr "읽기"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Read Messages"
msgstr "메시지 읽기"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Reason : %s"
msgstr "사유: %s"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__received
msgid "Received"
msgstr "수령함"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Receiving Messages"
msgstr "메시지 수신"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__red
msgid "Red"
msgstr "빨간색"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__rejected
msgid "Rejected"
msgstr "거부됨"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "Related %(model_name)s: "
msgstr "관련 %(model_name)s: "

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model
msgid "Related Document Model"
msgstr "관련 문서 모델"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__wa_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__wa_message_ids
msgid "Related WhatsApp Messages"
msgstr "관련된 WhatsApp 메시지"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__replied
msgid "Replied"
msgstr "회신됨"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__report_id
msgid "Report"
msgstr "보고서"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Reset to draft"
msgstr "초안으로 재설정"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__parent_id
msgid "Response To"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Retry"
msgstr "재시도"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ro
msgid "Romanian"
msgstr "루마니아어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ru
msgid "Russian"
msgstr "러시아어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 오류"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__demo_value
msgid "Sample Value"
msgstr "샘플값"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/web/messaging_menu_patch.xml:0
msgid "Search WhatsApp Channel"
msgstr "WhatsApp 채널 검색"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid "See all options"
msgstr "전체 선택 보기"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send Message"
msgstr "메시지 보내기"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__ir_actions_server__state__whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send WhatsApp"
msgstr "WhatsApp 전송"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.js:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.js:0
msgid "Send WhatsApp Message"
msgstr "WhatsApp 메시지 전송"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr "WhatsApp 전송 마법사"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Send and receive message through your WhatsApp Business account."
msgstr "WhatsApp 비즈니스 계정으로 메시지를 보내거나 받아 보세요."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Sending Messages"
msgstr "메시지 전송"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__sent
msgid "Sent"
msgstr "전송됨"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent Messages"
msgstr "메시지 전송"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent To"
msgstr "전송 대상:"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Sent to"
msgstr "전송 대상:"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__sequence
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__sequence
msgid "Sequence"
msgstr "순서"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sr
msgid "Serbian"
msgstr "세르비아어"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_ir_actions_server
msgid "Server Action"
msgstr "서버 작업"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sk
msgid "Slovak"
msgstr "슬로바키아어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sl
msgid "Slovenian"
msgstr "슬로베니아어"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid ""
"Something went wrong when contacting WhatsApp, please try again later. If "
"this happens frequently, contact support."
msgstr ""
"WhatsApp에 연락하는 동안 문제가 발생했습니다. 잠시 후 다시 시도하세요. 계속 반복될 경우에는 지원팀에 문의하시기 바랍니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es
msgid "Spanish"
msgstr "스페인어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_ar
msgid "Spanish (ARG)"
msgstr "스페인어 (ARG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_mx
msgid "Spanish (MEX)"
msgstr "스페인어 (MEX)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_es
msgid "Spanish (SPA)"
msgstr "스페인어 (SPA)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__state
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "State"
msgstr "시/도"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__static
msgid "Static"
msgstr "정적"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__status
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Status"
msgstr "상태"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Submit for Approval"
msgstr "제출 후 승인 대기 중"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sw
msgid "Swahili"
msgstr "스와힐리어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sv
msgid "Swedish"
msgstr "스웨덴"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Sync Template"
msgstr "서식 동기화"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Synchronize Templates"
msgstr "서식 동기화"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ta
msgid "Tamil"
msgstr "타밀어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__te
msgid "Telugu"
msgstr "텔루구어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Template"
msgstr "서식"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"Template %(template_name)s holds a wrong configuration for 'phone field'\n"
"%(error_msg)s"
msgstr ""
"%(template_name)s 서식에 '전화번호 필드' 설정이 잘못되었습니다.\n"
"%(error_msg)s"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_button_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_variable_view_form
msgid "Template Button"
msgstr "서식 버튼"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Template Guidelines"
msgstr "서식 가이드라인"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_text
msgid "Template Header Text"
msgstr "서식 머리글 텍스트"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_name
msgid "Template Name"
msgstr "서식명"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_preview_action_from_template
msgid "Template Preview"
msgstr "서식 미리보기"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__template
msgid "Template Quality Rating Too Low"
msgstr "서식 품질 평가가 너무 낮음"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_attachment_ids
msgid "Template Static Header"
msgstr "서식 동적 머리글"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__variable_ids
msgid "Template Variables"
msgstr "서식 변수"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__body
msgid "Template body"
msgstr "서식 본문"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Template category is missing"
msgstr "템플릿 카테고리가 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Template variable should be in format {{number}}. Cannot parse "
"\"%(placeholder)s\""
msgstr "서식 속성은 {{number}} 형식이어야 합니다. \"%(placeholder)s\"를 구문 분석할 수 없습니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__wa_template_id
#: model:ir.ui.menu,name:whatsapp.whatsapp_template_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Templates"
msgstr "템플릿(서식)"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates Of %(account_name)s"
msgstr "%(account_name)s의 서식"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Templates created on your"
msgstr "템플릿 생성"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates synchronized!"
msgstr "서식이 동기화되었습니다!"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Test Credentials"
msgstr "자격 증명 테스트"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__text
msgid "Text"
msgstr "문자"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__th
msgid "Thai"
msgstr "태국어"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"The Header Text must either contain no variable or the first one {{1}}."
msgstr "머리말 글에는 변수가 없거나 {{1}}이 첫 번째 변수여야 합니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "The phone number set in \"Buttons\" does not look correct."
msgstr "'버튼'에 설정된 전화번호가 올바르지 않습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "The placeholder for a button can only be {{1}}."
msgstr "버튼의 자리표지자로는 {{1}} 항목만 사용할 수 있습니다."

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_account_phone_uid_unique
msgid "The same phone number ID already exists"
msgstr "이미 존재하는 전화번호 아이디입니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There is no record for preparing demo pdf in model %(model)s"
msgstr "%(model)s 모델에 데모용 PDF를 준비한 기록이 없습니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "There might be other templates that still need the Multi"
msgstr "다중 항목이 필요한 다른 서식이 여전히 있을 수도 있습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There should be at most 1 variable in the header of the template."
msgstr "서식의 머리글 변수는 최대 1개만 있어야 합니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "This join method is not possible for regular channels."
msgstr "해당 들어가기 방법은 일반 채널에서는 사용할 수 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "To use WhatsApp Configure it first"
msgstr "사용하려면 먼저 WhatsApp 설정을 해야 합니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__tr
msgid "Turkish"
msgstr "터키어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__state
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__message_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__button_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_type
msgid "Type"
msgstr "유형"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,help:whatsapp.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"서버 작업 유형입니다. 다음 값을 사용할 수 있습니다:\n"
"- '레코드 업데이트': 레코드 값을 업데이트합니다.\n"
"- '활동 만들기': 활동을 생성합니다. (토론)\n"
"- '이메일 보내기': 메시지, 메모를 게시하거나 이메일을 전송합니다. (토론)\n"
"- 'SMS 보내기': SMS를 보내고, 문서에 기록합니다. (SMS)- '팔로워 추가/제거': 레코드에 팔로워를 추가하거나 제거합니다. (토론)\n"
"- '레코드 만들기': 새로운 값으로 새 레코드를 생성합니다.\n"
"- '코드 실행': 실행할 파이썬 코드 블록입니다.\n"
"- '웹훅 알림 보내기': 웹훅이라고도 하는 외부 시스템으로 POST 요청을 보냅니다.\n"
"- '기존 작업 실행': 여러 다른 서버 작업을 트리거하는 작업을 정의합니다.\n"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uk
msgid "Ukrainian"
msgstr "우크라이나어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__unknown
msgid "Unknown Error"
msgstr "알 수 없는 오류"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Unknown error when processing whatsapp request."
msgstr "WhatsApp 요청을 처리하는 중 알 수 없는 오류가 발생했습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ur
msgid "Urdu"
msgstr "우르두어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__url_type
msgid "Url Type"
msgstr "URL 유형"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,help:whatsapp.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"메세지 작성을 분류하는 데 사용됩니다.\n"
"'email': 수신 이메일에 의해 작성됩니다. (예: 메일 게이트웨이)\n"
"'comment': 토론 또는 작성란을 통해 사용자가 입력할 수 있습니다.\n"
"'email_outgoing': 메일링에 의해 작성됩니다.\n"
"'notification': 시스템에서 생성됩니다. (예: 메시지 추적)\n"
"'auto_comment': 자동화된 알림 메커니즘에 의해 생성됩니다. (예: 승인)\n"
"'user_notification': 특정 수신자를 위해 생성됩니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_mobile
msgid "User Mobile"
msgstr "사용자 핸드폰"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_name
msgid "User Name"
msgstr "사용자 이름"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_users_settings
msgid "User Settings"
msgstr "사용자 설정"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has been opt out of receiving WhatsApp messages"
msgstr "사용자가 WhatsApp 메시지 수신을 거부했습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has opted in to receiving WhatsApp messages"
msgstr "사용자가 WhatsApp 메시지 수신을 선택했습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"User mobile number required in template but no value set on user profile."
msgstr "서식에 사용자 핸드폰 번호가 있어야 합니다. 사용자 프로필에 설정된 값이 없습니다."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__allowed_user_ids
msgid "Users"
msgstr "사용자"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Users to notify is required"
msgstr "알림을 받을 사용자가 있어야 합니다."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__notify_user_ids
msgid ""
"Users to notify when a message is received and there is no template send in "
"last 15 days"
msgstr "메시지를 수신하였으나 지난 15일 동안 전송한 서식이 없을 경우 알림을 보낼 사용자입니다."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_message__failure_reason
msgid "Usually an error message from Whatsapp"
msgstr "보통 WhatsApp의 오류 메시지입니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__utility
msgid "Utility"
msgstr "유틸리티"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uz
msgid "Uzbek"
msgstr "우즈벡어"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__variable_ids
msgid "Variable"
msgstr "가변"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__line_type
msgid "Variable location"
msgstr "가변 위치"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_variable_name_type_template_unique
msgid "Variable names must be unique for a given template"
msgstr "해당 서식에 대해 변수 이름은 반드시 고유해야 합니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Variables %(field_names)s do not seem to be valid field path for model "
"%(model_name)s."
msgstr "변수 %(field_names)s가 %(model_name)s의 모델에 대한 유효한 필드 경로가 아닙니다.."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__video
msgid "Video"
msgstr "동영상"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__vi
msgid "Vietnamese"
msgstr "베트남어"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__url
msgid "Visit Website"
msgstr "웹사이트 방문"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__wa_template_id
msgid "Wa Template"
msgstr "Wa 서식"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__webhook_verify_token
msgid "Webhook Verify Token"
msgstr "웹훅 인증 토큰"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.xml:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.xml:0
#: code:addons/whatsapp/static/src/core/common/thread_icon_patch.xml:0
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/public_web/messaging_menu_patch.js:0
#: model:ir.model.fields.selection,name:whatsapp.selection__mail_message__message_type__whatsapp_message
#: model:ir.ui.menu,name:whatsapp.whatsapp_menu_main
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "WhatsApp"
msgstr "왓츠앱"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_actions_server_resend_whatsapp_queue
msgid "WhatsApp : Resend failed Messages"
msgstr "WhatsApp: 실패 메시지 재전송"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_cron_send_whatsapp_queue_ir_actions_server
msgid "WhatsApp : Send In Queue Messages"
msgstr "WhatsApp: 대기 중인 메시지 전송"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "WhatsApp Account"
msgstr "WhatsApp 계정"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_account_action
#: model:ir.model,name:whatsapp.model_whatsapp_account
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__wa_account_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account"
msgstr "WhatsApp 비즈니스 계정"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account ID"
msgstr "WhatsApp 비즈니스 계정 ID"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_account_menu
msgid "WhatsApp Business Accounts"
msgstr "WhatsApp 비즈니스 계정"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "WhatsApp Category Open"
msgstr "WhatsApp 카테고리 열기"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_partner__wa_channel_count
#: model:ir.model.fields,field_description:whatsapp.field_res_users__wa_channel_count
msgid "WhatsApp Channel Count"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_valid_until
msgid "WhatsApp Channel Valid Until Datetime"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "WhatsApp Chats"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__discuss_channel__channel_type__whatsapp
msgid "WhatsApp Conversation"
msgstr "왓츠앱 대화"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "WhatsApp Message"
msgstr "왓츠앱 메시지"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__msg_uid
msgid "WhatsApp Message ID"
msgstr "왓츠앱 메시지 ID"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_message_action
#: model:ir.model,name:whatsapp.model_whatsapp_message
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_graph
msgid "WhatsApp Messages"
msgstr "왓츠앱 메시지"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_partner_id
msgid "WhatsApp Partner"
msgstr "WhatsApp 파트너"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_template_action
#: model:ir.model,name:whatsapp.model_whatsapp_template
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_tree
msgid "WhatsApp Template"
msgstr "왓츠앱 서식"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_button
msgid "WhatsApp Template Button"
msgstr "왓츠앱 서식 버튼"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_template_uid
msgid "WhatsApp Template ID"
msgstr "왓츠앱 서식 ID"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_variable
msgid "WhatsApp Template Variable"
msgstr "WhatsApp 서식 속성"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/im_status_patch.xml:0
#: code:addons/whatsapp/static/src/discuss/core/common/channel_member_list_patch.xml:0
msgid "WhatsApp User"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp account is misconfigured or shared."
msgstr "WhatsApp 계정 설정이 잘못되었거나 공유되었습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp could not be reached or the query was malformed."
msgstr "WhatsApp에 연결할 수 없거나 잘못된 쿼리입니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"When using a \"location\" header, there should 4 location variables not "
"%(count)d."
msgstr "머리글로 '위치'를 사용할 경우, %(count)d 국가가 아닌 4개의 위치 변수를 사용해야 합니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__phone_invalid
msgid "Wrong Number Format"
msgstr "잘못된 번호 형식"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__yellow
msgid "Yellow"
msgstr "노랑"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You are not allowed to use %(field)s in phone field, contact your "
"administrator to configure it."
msgstr "%(field)s 필드 사용 권한이 없습니다. 관리자에게 연락하여 전화번호 필드를 설정하세요."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"You are not allowed to use field %(field)s, contact your administrator."
msgstr "%(field)s 필드 사용 권한이 없습니다. 관리자에게 문의하세요."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not cancel message which is in queue."
msgstr "대기 중인 메시지는 취소할 수 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not resend message which is not in failed state."
msgstr "실패 상태가 아닌 메시지는 재전송할 수 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "You can not select field of %(model)s."
msgstr "%(model)s 필드는 선택할 수 없습니다."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.js:0
msgid "You can set a maximum of 10 variables."
msgstr "최대 10개의 변수를 설정할 수 있습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid ""
"You can't leave this channel. As you are the owner of this WhatsApp channel,"
" you can only delete it."
msgstr "해당 채널에서 나갈 수 없습니다. 해당 WhatsApp 채널의 개설자이므로, 삭제만 할 수 있습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You cannot modify a template model when it is linked to server actions."
msgstr "서식 모델이 서버 작업과 연결되어 있는 경우에는 수정할 수 없습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "You may only use one header attachment for each template"
msgstr "서식에는 머리글 첨부 파일을 하나만 사용할 수 있습니다."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Your Template has been rejected."
msgstr "서식이 거부되었습니다."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zu
msgid "Zulu"
msgstr "줄루어"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "another document"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. \"Acme Inc. Business Account\""
msgstr "예: \"Acme Inc. 비즈니스 계정\""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. \"Send Order Document\""
msgstr "예: \"주문 문서 보내기\""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. 00112233445566778899aabbccddeeff"
msgstr "예: 00112233445566778899aabbccddeeff"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. ***************"
msgstr "예; ***************"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"
msgstr "예: EGTRWHRTHETHWRBTEJETHGQEGWRHWR"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. Invitation for {{1}}"
msgstr "예: {{1}} 관련 초대"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. https://www.example.com"
msgstr "e.g. https://www.example.com"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "platform then connect it to your Odoo database"
msgstr "플랫폼에 연결한 후 Odoo 데이터베이스에 연결합니다."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "recipients have an invalid phone number and will be skipped."
msgstr "수신자 전화번호가 잘못되었기 때문에 이 문자 메시지를 건너뜁니다."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "variable"
msgstr "가변"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid ""
"will be visible here once they're synced.\n"
"                You can also write new ones from here and submit them for approval, following the"
msgstr ""
"동기화되면 여기에 표시됩니다.\n"
"                또한 여기에서 새 문서를 작성하여 승인을 받기 위해 제출할 수도 있습니다."
