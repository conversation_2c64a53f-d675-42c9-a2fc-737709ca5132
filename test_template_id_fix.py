#!/usr/bin/env python3
"""
Test script to verify the template ID fix for Taqnyat API alphanumeric template IDs
This script simulates different template ID scenarios to test the robust handling
"""

import json

def test_template_id_handling():
    """Test different template ID scenarios"""
    
    print("🆔 Testing Template ID Handling")
    print("=" * 50)
    
    # Test cases for different template ID formats
    test_cases = [
        {
            'name': 'Taqnyat alphanumeric ID',
            'remote_template_vals': {
                'id': '0B682lVKZjxzaBIhsXBnWT',
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'expected_wa_template_uid': '0B682lVKZjxzaBIhsXBnWT'
        },
        {
            'name': 'Facebook numeric ID as string',
            'remote_template_vals': {
                'id': '*********',
                'name': 'test_template',
                'language': 'en_US',
                'status': 'APPROVED',
                'category': 'UTILITY'
            },
            'expected_wa_template_uid': '*********'
        },
        {
            'name': 'Facebook numeric ID as integer',
            'remote_template_vals': {
                'id': 987654321,
                'name': 'test_template',
                'language': 'en_US',
                'status': 'APPROVED',
                'category': 'UTILITY'
            },
            'expected_wa_template_uid': '987654321'
        },
        {
            'name': 'Mixed alphanumeric ID',
            'remote_template_vals': {
                'id': 'ABC123def456',
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'expected_wa_template_uid': 'ABC123def456'
        },
        {
            'name': 'Short numeric ID',
            'remote_template_vals': {
                'id': '123',
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'expected_wa_template_uid': '123'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        remote_template_vals = test_case['remote_template_vals']
        expected_wa_template_uid = test_case['expected_wa_template_uid']
        
        # Simulate the fixed template ID handling logic
        wa_template_uid = str(remote_template_vals['id'])
        
        print(f"Test {i}: {test_case['name']}")
        print(f"  Input ID: {remote_template_vals['id']} (type: {type(remote_template_vals['id']).__name__})")
        print(f"  Processed wa_template_uid: {wa_template_uid}")
        print(f"  Expected wa_template_uid: {expected_wa_template_uid}")
        print(f"  ✅ PASS" if wa_template_uid == expected_wa_template_uid else f"  ❌ FAIL")
        print()

def test_template_vals_creation_with_ids():
    """Test complete template_vals creation with different ID types"""
    
    print("📋 Testing Template Vals Creation with Different ID Types")
    print("=" * 60)
    
    # Mock wa_account
    class MockWaAccount:
        def __init__(self):
            self.id = 1
    
    wa_account = MockWaAccount()
    
    # Test cases
    test_templates = [
        {
            'name': 'Taqnyat alphanumeric ID',
            'template': {
                'id': '0B682lVKZjxzaBIhsXBnWT',
                'name': 'taqnyat_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING',
                'components': []
            }
        },
        {
            'name': 'Facebook numeric ID',
            'template': {
                'id': *********,
                'name': 'facebook_template',
                'language': 'en_US',
                'status': 'APPROVED',
                'category': 'UTILITY',
                'components': []
            }
        }
    ]
    
    # Simulate the fixed _get_template_vals_from_response logic
    def get_template_vals_from_response(remote_template_vals, wa_account):
        # Handle quality_score safely - Taqnyat API may not include this field
        quality_score = 'unknown'  # Default value
        if remote_template_vals.get('quality_score'):
            if isinstance(remote_template_vals['quality_score'], dict):
                quality_score = remote_template_vals['quality_score'].get('score', 'unknown').lower()
            else:
                # Handle case where quality_score is a string directly
                quality_score = str(remote_template_vals['quality_score']).lower()
        
        template_vals = {
            'body': False,
            'button_ids': [],
            'footer_text': False,
            'header_text': False,
            'header_attachment_ids': [],
            'header_type': 'none',
            'lang_code': remote_template_vals['language'],
            'name': remote_template_vals['name'].replace("_", " ").title(),
            'quality': 'none' if quality_score == 'unknown' else quality_score,
            'status': remote_template_vals['status'].lower(),
            'template_name': remote_template_vals['name'],
            'template_type': remote_template_vals['category'].lower(),
            'variable_ids': [],
            'wa_account_id': wa_account.id,
            'wa_template_uid': str(remote_template_vals['id']),  # Fixed: use str() instead of int()
        }
        return template_vals
    
    for test_case in test_templates:
        template = test_case['template']
        
        try:
            template_vals = get_template_vals_from_response(template, wa_account)
            
            print(f"✅ {test_case['name']}:")
            print(f"  Input ID: {template['id']} (type: {type(template['id']).__name__})")
            print(f"  Output wa_template_uid: {template_vals['wa_template_uid']} (type: {type(template_vals['wa_template_uid']).__name__})")
            print(f"  Template name: {template_vals['name']}")
            print(f"  Status: {template_vals['status']}")
            print(f"  Quality: {template_vals['quality']}")
            print()
            
            # Verify key assertions
            assert isinstance(template_vals['wa_template_uid'], str), f"wa_template_uid should be string, got {type(template_vals['wa_template_uid'])}"
            assert template_vals['wa_template_uid'] == str(template['id']), f"wa_template_uid mismatch: {template_vals['wa_template_uid']} != {str(template['id'])}"
            
        except Exception as e:
            print(f"❌ {test_case['name']} FAILED: {e}")
            print()

def test_error_scenarios():
    """Test error scenarios that should be handled gracefully"""
    
    print("🚨 Testing Error Scenarios")
    print("=" * 50)
    
    error_cases = [
        {
            'name': 'ID is None',
            'template': {
                'id': None,
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'should_fail': True
        },
        {
            'name': 'ID is empty string',
            'template': {
                'id': '',
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'expected_result': ''
        },
        {
            'name': 'ID is zero',
            'template': {
                'id': 0,
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'expected_result': '0'
        },
        {
            'name': 'ID with special characters',
            'template': {
                'id': 'ABC-123_def.456',
                'name': 'test_template',
                'language': 'ar',
                'status': 'APPROVED',
                'category': 'MARKETING'
            },
            'expected_result': 'ABC-123_def.456'
        }
    ]
    
    for i, test_case in enumerate(error_cases, 1):
        template = test_case['template']
        
        try:
            # Simulate the handling logic
            wa_template_uid = str(template['id'])
            
            if test_case.get('should_fail'):
                print(f"Test {i}: {test_case['name']} - Expected to fail but didn't")
                print(f"  Result: {wa_template_uid}")
                print(f"  ❌ UNEXPECTED PASS")
            else:
                expected_result = test_case.get('expected_result')
                print(f"Test {i}: {test_case['name']}")
                print(f"  Input: {template['id']}")
                print(f"  Expected: {expected_result}")
                print(f"  Actual: {wa_template_uid}")
                print(f"  ✅ PASS" if wa_template_uid == expected_result else f"  ❌ FAIL")
            print()
            
        except Exception as e:
            if test_case.get('should_fail'):
                print(f"Test {i}: {test_case['name']}")
                print(f"  Expected to fail: ✅ PASS")
                print(f"  Error: {e}")
            else:
                print(f"Test {i}: {test_case['name']}")
                print(f"  Unexpected error: ❌ FAIL")
                print(f"  Error: {e}")
            print()

def main():
    """Run all tests"""
    print("🧪 Template ID Fix Tests")
    print("=" * 60)
    print()
    
    test_template_id_handling()
    test_template_vals_creation_with_ids()
    test_error_scenarios()
    
    print("🎉 All tests completed!")
    print()
    print("📝 Summary of fix:")
    print("1. ✅ Changed int(remote_template_vals['id']) to str(remote_template_vals['id'])")
    print("2. ✅ Handles both numeric and alphanumeric template IDs")
    print("3. ✅ Compatible with Taqnyat's alphanumeric IDs (e.g., '0B682lVKZjxzaBIhsXBnWT')")
    print("4. ✅ Maintains backward compatibility with Facebook's numeric IDs")
    print("5. ✅ wa_template_uid field is correctly defined as Char field")
    print()
    print("🚀 Template sync should now work with Taqnyat's alphanumeric template IDs!")

if __name__ == "__main__":
    main()
