from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime
import requests
import logging
from odoo.addons.whatsapp.tools.phone_validator import PhoneNumberValidator, OptInManager

_logger = logging.getLogger(__name__)

class WhatsAppOptIn(models.Model):
    _name = 'whatsapp.optin'
    _description = 'WhatsApp Opt-In Management'
    _rec_name = 'mobile_number'
    _order = 'write_date desc'

    # Enhanced fields for comprehensive opt-in management
    name = fields.Char('Customer Name')
    mobile_number = fields.Char('Mobile Number', required=True, help="Phone number in international format without + prefix")
    phone = fields.Char('Mobile Number (Legacy)', help="Legacy field for backward compatibility")
    wa_account_id = fields.Many2one('whatsapp.account', required=True, string="WhatsApp Account")

    # Opt-in status and tracking
    is_opted_in = fields.Boolean('Opted In', default=False)
    opt_in_date = fields.Datetime('Opt-In Date')
    opt_out_date = fields.Datetime('Opt-Out Date')
    source = fields.Selection([
        ('manual', 'Manual'),
        ('website', 'Website Form'),
        ('api', 'API'),
        ('bulk_import', 'Bulk Import'),
        ('inbound_message', 'Inbound Message'),
        ('qr_code', 'QR Code'),
        ('other', 'Other')
    ], string='Opt-In Source', default='manual')

    # Marketing preferences
    allow_marketing = fields.Boolean('Allow Marketing Messages', default=True)
    allow_notifications = fields.Boolean('Allow Notifications', default=True)

    # Legacy status field for backward compatibility
    status = fields.Selection([
        ('draft', 'Draft'),
        ('sent', 'Opted-In'),
        ('fail', 'Failed')
    ], default='draft', string='Legacy Status')

    # Additional tracking
    opt_out_reason = fields.Char('Opt-Out Reason')
    message = fields.Text('Response Message')
    country_code = fields.Char('Country Code', size=2)
    partner_id = fields.Many2one('res.partner', string='Contact')

    # Computed fields
    display_phone = fields.Char('Display Phone', compute='_compute_display_phone', store=True)
    can_send_marketing = fields.Boolean('Can Send Marketing', compute='_compute_permissions')
    can_send_utility = fields.Boolean('Can Send Utility', compute='_compute_permissions')

    def button_send_optin(self):
        self.ensure_one()

        if not self.phone.startswith('966'):
            raise ValidationError("Phone must be in international format, e.g., 9665XXXXXXX")

        token = self.wa_account_id.token
        if not token:
            raise ValidationError("Missing WhatsApp API token in the selected account.")

        url = 'https://api.taqnyat.sa/wa/v2/contacts/opt-in/'
        headers = {
            'Authorization': token,
            'Content-Type': 'application/json',
        }
        data = {
            "msisdn": self.phone,
            "opted_in": True
        }

        try:
            response = requests.post(url, json=data, headers=headers, timeout=20)
            _logger.info("Taqnyat Opt-In response: %s", response.text)

            if response.status_code == 200:
                self.status = 'sent'
                self.message = response.text
            else:
                self.status = 'fail'
                self.message = f"Failed with status {response.status_code}: {response.text[:200]}"

        except requests.exceptions.RequestException as e:
            _logger.error("Opt-In request error: %s", e)
            self.status = 'fail'
            self.message = f"Request error: {str(e)}"

    @api.depends('mobile_number')
    def _compute_display_phone(self):
        """Compute display phone number with + prefix"""
        for record in self:
            if record.mobile_number:
                try:
                    record.display_phone = PhoneNumberValidator.format_for_display(record.mobile_number)
                except:
                    record.display_phone = record.mobile_number
            else:
                record.display_phone = ''

    @api.depends('is_opted_in', 'allow_marketing', 'opt_out_date')
    def _compute_permissions(self):
        """Compute message sending permissions"""
        for record in self:
            record.can_send_marketing = record.is_opted_in and record.allow_marketing
            record.can_send_utility = record.is_opted_in or not record.opt_out_date

    @api.model_create_multi
    def create(self, vals_list):
        """Enhanced create with phone validation"""
        for vals in vals_list:
            # Validate and clean phone number
            if vals.get('mobile_number'):
                try:
                    is_valid, cleaned, country = PhoneNumberValidator.validate_phone_format(vals['mobile_number'])
                    if is_valid:
                        vals['mobile_number'] = cleaned
                        if country:
                            vals['country_code'] = country
                    else:
                        raise ValidationError(_("Invalid phone number format: %s") % vals['mobile_number'])
                except Exception as e:
                    raise ValidationError(_("Phone validation error: %s") % str(e))

            # Handle legacy phone field
            elif vals.get('phone'):
                vals['mobile_number'] = vals['phone']
                try:
                    is_valid, cleaned, country = PhoneNumberValidator.validate_phone_format(vals['phone'])
                    if is_valid:
                        vals['mobile_number'] = cleaned
                        if country:
                            vals['country_code'] = country
                except:
                    pass  # Keep original for legacy compatibility

        return super().create(vals_list)

    def write(self, vals):
        """Enhanced write with phone validation"""
        if vals.get('mobile_number'):
            try:
                is_valid, cleaned, country = PhoneNumberValidator.validate_phone_format(vals['mobile_number'])
                if is_valid:
                    vals['mobile_number'] = cleaned
                    if country:
                        vals['country_code'] = country
                else:
                    raise ValidationError(_("Invalid phone number format: %s") % vals['mobile_number'])
            except Exception as e:
                raise ValidationError(_("Phone validation error: %s") % str(e))

        return super().write(vals)

    def action_opt_in(self):
        """Action to opt-in contact"""
        self.ensure_one()
        manager = OptInManager(self.env)
        manager.record_opt_in(
            self.mobile_number,
            self.wa_account_id.id,
            self.source or 'manual',
            self.allow_marketing
        )
        return True

    def action_opt_out(self, reason='user_request'):
        """Action to opt-out contact"""
        self.ensure_one()
        manager = OptInManager(self.env)
        manager.record_opt_out(self.mobile_number, self.wa_account_id.id, reason)
        return True

    def check_message_permission(self, message_category='utility'):
        """Check if message can be sent to this contact"""
        self.ensure_one()
        manager = OptInManager(self.env)
        return manager.can_send_message(self.mobile_number, self.wa_account_id.id, message_category)

    @api.model
    def get_opt_in_statistics(self, wa_account_id, days=30):
        """Get opt-in statistics for account"""
        manager = OptInManager(self.env)
        return manager.get_opt_in_statistics(wa_account_id, days)

    @api.model
    def bulk_opt_in_from_list(self, phone_numbers, wa_account_id, source='bulk_import'):
        """Bulk opt-in from phone number list"""
        manager = OptInManager(self.env)
        return manager.bulk_opt_in(phone_numbers, wa_account_id, source)
