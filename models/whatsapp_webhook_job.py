# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models, api, _
import json
import logging
import hashlib

_logger = logging.getLogger(__name__)

class WhatsappWebhookJob(models.Model):
    _name = 'whatsapp.webhook.job'
    _description = 'Whatsapp Webhook Job Queue'
    _order = 'create_date asc'

    name = fields.Char(string='Job Name', required=True)
    webhook_data = fields.Json(string='Webhook Data', required=True)
    webhook_type = fields.Selection([
        ('taqnyat', 'Taqnyat Webhook'),
        ('meta', 'Meta Webhook'),
    ], string='Webhook Type', required=True)
    state = fields.Selection([
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('done', 'Done'),
        ('failed', 'Failed'),
    ], default='pending', string='Status', required=True)
    failure_reason = fields.Text(string='Failure Reason')
    retry_count = fields.Integer(string='Retry Count', default=0)
    last_retry_date = fields.Datetime(string='Last Retry Date')
    webhook_data_hash = fields.Char(string='Webhook Data Hash', index=True, copy=False)

    @api.model
    def _process_pending_webhooks(self):
        """ Cron job to process pending webhook jobs """
        _logger.info("Starting webhook job processing cron.")
        jobs = self.search([('state', '=', 'pending')], limit=10) # Process in batches
        for job in jobs:
            job.state = 'processing'
            try:
                if job.webhook_type == 'taqnyat':
                    self._process_taqnyat_webhook_job(job)
                elif job.webhook_type == 'meta':
                    self._process_meta_webhook_job(job)
                job.state = 'done'
            except Exception as e:
                job.state = 'failed'
                job.failure_reason = str(e)
                job.retry_count += 1
                job.last_retry_date = fields.Datetime.now()
                _logger.error("Error processing webhook job %s: %s", job.name, str(e), exc_info=True)
        _logger.info("Finished webhook job processing cron.")

    def _process_taqnyat_webhook_job(self, job):
        """ Process a single Taqnyat webhook job """
        data = job.webhook_data
        _logger.info("Processing Taqnyat webhook job %s: %s", job.name, json.dumps(data, indent=2))

        # Implement ID-based deduplication for Taqnyat webhooks
        # For now, we'll use a simple hash of the entire data as a unique identifier for deduplication
        webhook_id = hashlib.sha256(json.dumps(data, sort_keys=True).encode()).hexdigest()
        if self.env['whatsapp.webhook.job'].search_count([('webhook_type', '=', 'taqnyat'), ('webhook_data_hash', '=', webhook_id), ('state', '=', 'done')], limit=1):
            _logger.info("Duplicate Taqnyat webhook job %s detected. Skipping.", job.name)
            job.state = 'done' # Mark as done if duplicate
            return
        job.webhook_data_hash = webhook_id # Store hash for deduplication

        # Re-use the logic from the controller's _process_taqnyat_webhook
        wa_account_id = self.env['whatsapp.account'].sudo().search([('active', '=', True)], limit=1)
        if not wa_account_id:
            _logger.warning("No active WhatsApp account found for Taqnyat webhook job %s", job.name)
            return

        if data.get('statuses'):
            for status in data['statuses']:
                self.env['whatsapp.message']._process_taqnyat_status(status)

        if data.get('notifications'):
            for notification in data['notifications']:
                wa_account_id._process_taqnyat_message(notification)

    def _process_meta_webhook_job(self, job):
        """ Process a single Meta webhook job """
        data = job.webhook_data
        _logger.info("Processing Meta webhook job %s: %s", job.name, json.dumps(data, indent=2))

        # Implement ID-based deduplication for Meta webhooks
        # For now, we'll use a simple hash of the entire data as a unique identifier for deduplication
        webhook_id = hashlib.sha256(json.dumps(data, sort_keys=True).encode()).hexdigest()
        if self.env['whatsapp.webhook.job'].search_count([('webhook_type', '=', 'meta'), ('webhook_data_hash', '=', webhook_id), ('state', '=', 'done')], limit=1):
            _logger.info("Duplicate Meta webhook job %s detected. Skipping.", job.name)
            job.state = 'done' # Mark as done if duplicate
            return
        job.webhook_data_hash = webhook_id # Store hash for deduplication

        # Re-use the logic from the controller's _process_meta_webhook
        for entry in data.get('entry', []):
            account_id = entry['id']
            account = self.env['whatsapp.account'].sudo().search([('account_uid', '=', account_id)])
            # Note: Signature check is done at the controller level before enqueuing

            for changes in entry.get('changes', []):
                value = changes['value']
                phone_number_id = value.get('metadata', {}).get('phone_number_id', {})
                if not phone_number_id:
                    phone_number_id = value.get('whatsapp_business_api_data', {}).get('phone_number_id', {})
                if phone_number_id:
                    wa_account_id = self.env['whatsapp.account'].sudo().search([
                        ('phone_uid', '=', phone_number_id), ('account_uid', '=', account_id)])
                    if wa_account_id:
                        if changes['field'] == 'messages':
                            self.env['whatsapp.message']._process_statuses(value)
                            wa_account_id._process_messages(value)
                    else:
                        _logger.warning("There is no phone configured for this whatsapp webhook : %s ", data)

                if value.get('message_template_id'):
                    template = self.env['whatsapp.template'].sudo().with_context(active_test=False).search([('wa_template_uid', '=', value['message_template_id'])])
                    if template:
                        if changes['field'] == 'message_template_status_update':
                            template.write({'status': value['event'].lower()})
                            if value['event'].lower() == 'rejected':
                                body = _("Your Template has= Your Template has been rejected.")
                                description = value.get('other_info', {}).get('description') or value.get('reason')
                                if description:
                                    body += Markup("<br/>") + _("Reason : %s", description)
                                template.message_post(body=body)
                            continue
                        if changes['field'] == 'message_template_quality_update':
                            new_quality_score = value['new_quality_score'].lower()
                            new_quality_score = {'unknown': 'none'}.get(new_quality_score, new_quality_score)
                            template.write({'quality': new_quality_score})
                            continue
                        if changes['field'] == 'template_category_update':
                            template.write({'template_type': value['new_category'].lower()})
                            continue
                        _logger.warning("Unknown Template webhook : %s ", value)
                    else:
                        _logger.warning("No Template found for this webhook : %s ", value)


