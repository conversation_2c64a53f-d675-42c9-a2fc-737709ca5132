# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
import re
from markupsafe import Markup

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WhatsAppDirectMessage(models.TransientModel):
    """Model for sending direct WhatsApp messages from chat interface"""
    _name = 'whatsapp.direct.message'
    _description = 'WhatsApp Direct Message Composer'

    # Basic fields
    phone_number = fields.Char(
        string='Phone Number',
        required=True,
        help='Phone number with country code (e.g., +************)'
    )
    message_body = fields.Text(
        string='Message',
        required=True,
        help='Message content to send via WhatsApp'
    )
    wa_account_id = fields.Many2one(
        'whatsapp.account',
        string='WhatsApp Account',
        required=True,
        help='WhatsApp Business Account to use for sending'
    )
    
    # Context fields
    thread_model = fields.Char(string='Thread Model')
    thread_id = fields.Integer(string='Thread ID')
    partner_id = fields.Many2one('res.partner', string='Contact')
    
    # Status fields
    state = fields.Selection([
        ('draft', 'Draft'),
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('error', 'Error')
    ], default='draft', string='Status')
    
    error_message = fields.Text(string='Error Message')
    message_id = fields.Char(string='WhatsApp Message ID')

    @api.model
    def default_get(self, fields_list):
        """Set default values based on context"""
        defaults = super().default_get(fields_list)
        
        # Get default WhatsApp account
        wa_account = self.env['whatsapp.account'].search([
            ('allowed_company_ids', 'in', self.env.company.ids)
        ], limit=1)
        if wa_account:
            defaults['wa_account_id'] = wa_account.id
        
        # Get context values
        context = self.env.context
        if context.get('thread_model'):
            defaults['thread_model'] = context['thread_model']
        if context.get('thread_id'):
            defaults['thread_id'] = context['thread_id']
        if context.get('partner_id'):
            defaults['partner_id'] = context['partner_id']
            
        # Try to get phone number from partner
        if context.get('partner_id'):
            partner = self.env['res.partner'].browse(context['partner_id'])
            if partner.mobile:
                defaults['phone_number'] = partner.mobile
            elif partner.phone:
                defaults['phone_number'] = partner.phone
                
        return defaults

    @api.constrains('phone_number')
    def _check_phone_number(self):
        """Validate phone number format"""
        for record in self:
            if record.phone_number:
                # Basic phone number validation
                phone = record.phone_number.strip()
                if not re.match(r'^\+?[1-9]\d{1,14}$', phone):
                    raise ValidationError(_('Please enter a valid phone number with country code (e.g., +************)'))

    def _format_phone_number(self):
        """Format phone number for WhatsApp API"""
        phone = self.phone_number.strip()
        # Ensure it starts with +
        if not phone.startswith('+'):
            if phone.startswith('00'):
                phone = '+' + phone[2:]
            elif phone.startswith('0'):
                # This is tricky - we need country code
                # For now, assume Saudi Arabia if no country code
                phone = '+966' + phone[1:]
            else:
                phone = '+' + phone
        return phone

    def action_send_message(self):
        """Send the WhatsApp message"""
        self.ensure_one()

        if not self.wa_account_id:
            raise UserError(_('Please select a WhatsApp account'))

        if not self.wa_account_id.token:
            raise UserError(_('WhatsApp account is not properly configured. Please check the Bearer token.'))

        try:
            # Import here to avoid circular imports
            from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
            from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError

            self.state = 'sending'

            # Format phone number
            formatted_phone = self._format_phone_number()

            # Initialize WhatsApp API
            wa_api = WhatsAppApi(self.wa_account_id)

            # Send message using the direct message API
            response = wa_api.send_direct_message(formatted_phone, self.message_body)

            # Process response
            if response.get('statuses') and response['statuses'].get('message_id'):
                self.message_id = response['statuses']['message_id']
                self.state = 'sent'

                # Create a WhatsApp message record for tracking
                self._create_whatsapp_message_record(response)

                # Post message to thread if applicable
                self._post_to_thread()

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'type': 'success',
                        'title': _('WhatsApp Message Sent'),
                        'message': _('Message sent successfully to %s') % formatted_phone,
                        'next': {'type': 'ir.actions.act_window_close'},
                    }
                }
            else:
                raise WhatsAppError(_('Failed to send message: Invalid response from WhatsApp API'))

        except WhatsAppError as e:
            self.state = 'error'
            self.error_message = str(e)
            _logger.error("WhatsApp direct message failed: %s", str(e))
            raise UserError(_('Failed to send WhatsApp message: %s') % str(e))
        except Exception as e:
            self.state = 'error'
            self.error_message = str(e)
            _logger.error("Unexpected error sending WhatsApp message: %s", str(e))
            raise UserError(_('Unexpected error: %s') % str(e))

    def _create_whatsapp_message_record(self, api_response):
        """Create a WhatsApp message record for tracking"""
        try:
            # Create mail message first
            mail_message = self.env['mail.message'].create({
                'subject': _('WhatsApp Message'),
                'body': Markup('<p>%s</p>') % self.message_body,
                'message_type': 'whatsapp_message',
                'author_id': self.env.user.partner_id.id,
                'res_id': self.thread_id if self.thread_model else False,
                'model': self.thread_model if self.thread_model else False,
            })
            
            # Create WhatsApp message record
            whatsapp_message = self.env['whatsapp.message'].create({
                'mail_message_id': mail_message.id,
                'body': self.message_body,
                'mobile_number': self.phone_number,
                'mobile_number_formatted': self._format_phone_number(),
                'wa_account_id': self.wa_account_id.id,
                'message_type': 'outbound',
                'state': 'sent',
                'msg_uid': self.message_id,
            })
            
            return whatsapp_message
            
        except Exception as e:
            _logger.warning("Failed to create WhatsApp message record: %s", str(e))
            return False

    def _post_to_thread(self):
        """Post the message to the related thread/channel"""
        if not (self.thread_model and self.thread_id):
            return
            
        try:
            # Get the record
            record = self.env[self.thread_model].browse(self.thread_id)
            if not record.exists():
                return
                
            # Post message to the thread
            if hasattr(record, 'message_post'):
                body = Markup('<p><i class="fa fa-whatsapp text-success"></i> WhatsApp message sent to %s:</p><p>%s</p>') % (
                    self.phone_number, self.message_body
                )
                record.message_post(
                    body=body,
                    message_type='notification',
                    subtype_xmlid='mail.mt_note'
                )
                
        except Exception as e:
            _logger.warning("Failed to post message to thread: %s", str(e))

    def action_retry(self):
        """Retry sending the message"""
        self.state = 'draft'
        self.error_message = False
        return self.action_send_message()
