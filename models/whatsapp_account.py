# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
import mimetypes
import secrets
import string
from markupsafe import Markup

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError
from odoo.tools import plaintext2html

_logger = logging.getLogger(__name__)


class WhatsAppAccount(models.Model):
    _name = 'whatsapp.account'
    _inherit = ['mail.thread']
    _description = 'WhatsApp Business Account'

    name = fields.Char(string="Name", tracking=1)
    active = fields.Boolean(default=True, tracking=6)

    # Taqnyat.sa only requires Bearer token - keeping old fields for backward compatibility
    app_uid = fields.Char(string="App ID (Legacy)", help="Not used with Taqnyat.sa API", tracking=2)
    app_secret = fields.Char(string="App Secret (Legacy)", groups='whatsapp.group_whatsapp_admin', help="Not used with Taqnyat.sa API")
    account_uid = fields.Char(string="Account ID (Legacy)", help="Not used with Taqnyat.sa API", tracking=3)
    phone_uid = fields.Char(string="Phone Number ID (Legacy)", help="Not used with Taqnyat.sa API", tracking=4)

    # Main authentication field for Taqnyat.sa
    token = fields.Char(string="Bearer Token", groups='whatsapp.group_whatsapp_admin',
                       help="Bearer token from Taqnyat.sa portal. Get this from https://portal.taqnyat.sa → Developer → Applications → WhatsApp Service")
    webhook_verify_token = fields.Char(string="Webhook Verify Token", compute='_compute_verify_token',
                                       groups='whatsapp.group_whatsapp_admin', store=True)
    callback_url = fields.Char(string="Callback URL", compute='_compute_callback_url', readonly=True, copy=False)

    allowed_company_ids = fields.Many2many(
        comodel_name='res.company', string="Allowed Company",
        default=lambda self: self.env.company)
    notify_user_ids = fields.Many2many(
        comodel_name='res.users', default=lambda self: self.env.user,
        domain=[('share', '=', False)], required=True, tracking=5,
        help="Users to notify when a message is received and there is no template send in last 15 days")

    templates_count = fields.Integer(string="Message Count", compute='_compute_templates_count')

    # Performance and caching fields
    last_sync_date = fields.Datetime(string="Last Template Sync", readonly=True,
                                    help="Last time templates were synchronized from Taqnyat.sa")
    last_connection_test = fields.Datetime(string="Last Connection Test", readonly=True,
                                          help="Last time connection was successfully tested")
    api_call_count = fields.Integer(string="API Calls Today", default=0, readonly=True,
                                   help="Number of API calls made today (resets daily)")
    last_api_reset = fields.Date(string="Last API Reset", default=fields.Date.today, readonly=True)

    # Indexing and search optimization
    search_keywords = fields.Text(string="Search Keywords", compute='_compute_search_keywords', store=True,
                                 help="Computed field for enhanced search capabilities")

    _sql_constraints = [
        ('token_unique', 'unique(token)', "The same Bearer token already exists")]

    @api.constrains('notify_user_ids')
    def _check_notify_user_ids(self):
        for phone in self:
            if len(phone.notify_user_ids) < 1:
                raise ValidationError(_("Users to notify is required"))

    @api.constrains('token', 'app_uid', 'app_secret', 'account_uid', 'phone_uid')
    def _check_authentication_config(self):
        for account in self:
            has_taqnyat = bool(account.token)
            has_meta = bool(account.app_uid and account.app_secret and account.account_uid and account.phone_uid)

            if not has_taqnyat and not has_meta:
                raise ValidationError(_(
                    "Please configure authentication:\n"
                    "• For new setups: Enter your Taqnyat.sa Bearer Token\n"
                    "• For legacy setups: Enter all Meta API credentials (App ID, App Secret, Account ID, Phone Number ID)"
                ))

            if has_taqnyat and len(account.token) < 20:
                raise ValidationError(_(
                    "Bearer Token appears to be too short. Please verify your token from Taqnyat.sa portal.\n\n"
                    "To get your Bearer token:\n"
                    "1. Login to https://portal.taqnyat.sa\n"
                    "2. Go to Developer → Applications\n"
                    "3. Create a new application for WhatsApp Service\n"
                    "4. Copy the Bearer Token (should be 30+ characters)"
                ))

    def _compute_callback_url(self):
        for account in self:
            account.callback_url = self.get_base_url() + '/whatsapp/webhook'

    @api.depends('account_uid')
    def _compute_verify_token(self):
        """ webhook_verify_token only set when record is created. Not update after that."""
        for rec in self:
            if rec.id and not rec.webhook_verify_token:
                rec.webhook_verify_token = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))

    def _compute_templates_count(self):
        for tmpl in self:
            tmpl.templates_count = self.env['whatsapp.template'].search_count([('wa_account_id', '=', tmpl.id)])

    @api.depends('name', 'token', 'phone_uid', 'account_uid')
    def _compute_search_keywords(self):
        """Compute search keywords for enhanced search capabilities"""
        for account in self:
            keywords = []
            if account.name:
                keywords.append(account.name.lower())
            if account.token:
                # Add first and last 5 characters of token for partial search
                keywords.append(account.token[:5].lower())
                keywords.append(account.token[-5:].lower())
            if account.phone_uid:
                keywords.append(account.phone_uid)
            if account.account_uid:
                keywords.append(account.account_uid)

            # Add provider type
            if account.token:
                keywords.append('taqnyat')
            if account.app_uid:
                keywords.append('meta')
                keywords.append('facebook')

            account.search_keywords = ' '.join(keywords)

    def _reset_daily_api_counter(self):
        """Reset API call counter daily"""
        today = fields.Date.today()
        accounts_to_reset = self.search([('last_api_reset', '<', today)])
        accounts_to_reset.write({
            'api_call_count': 0,
            'last_api_reset': today
        })

    def _increment_api_counter(self):
        """Increment API call counter for rate limiting and monitoring"""
        self.ensure_one()
        today = fields.Date.today()
        if self.last_api_reset < today:
            self.write({
                'api_call_count': 1,
                'last_api_reset': today
            })
        else:
            self.write({'api_call_count': self.api_call_count + 1})

    def button_sync_whatsapp_account_templates(self):
        """
            This method will sync all the templates of the WhatsApp Business Account.
            It will create new templates and update existing templates.
            Enhanced with performance tracking and caching.
        """
        self.ensure_one()
        sync_start_time = fields.Datetime.now()

        try:
            # Increment API counter for monitoring
            self._increment_api_counter()

            response = WhatsAppApi(self)._get_all_template(fetch_all=True)
        except WhatsAppError as err:
            raise ValidationError(str(err)) from err

        WhatsappTemplate = self.env['whatsapp.template']
        # Use indexed search for better performance
        existing_tmpls = WhatsappTemplate.with_context(active_test=False).search([
            ('wa_account_id', '=', self.id)
        ])
        existing_tmpl_by_id = {t.wa_template_uid: t for t in existing_tmpls}
        template_update_count = 0
        template_create_count = 0

        if response.get('data'):
            create_vals = []
            # Process templates in batches for better performance
            batch_size = 50
            templates = response['data']

            for i in range(0, len(templates), batch_size):
                batch = templates[i:i + batch_size]
                for template in batch:
                    existing_tmpl = existing_tmpl_by_id.get(template['id'])
                    if existing_tmpl:
                        template_update_count += 1
                        existing_tmpl._update_template_from_response(template)
                    else:
                        template_create_count += 1
                        create_vals.append(WhatsappTemplate._create_template_from_response(template, self))

                # Create templates in batches
                if create_vals and len(create_vals) >= batch_size:
                    WhatsappTemplate.create(create_vals)
                    create_vals = []

            # Create remaining templates
            if create_vals:
                WhatsappTemplate.create(create_vals)

        # Update sync timestamp
        self.write({'last_sync_date': sync_start_time})

        sync_duration = (fields.Datetime.now() - sync_start_time).total_seconds()
        _logger.info("Template sync completed for account %s in %.2f seconds. Created: %d, Updated: %d",
                    self.name, sync_duration, template_create_count, template_update_count)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _("Templates synchronized!"),
                'type': 'success',
                'message': _("%(create_count)s were created, %(update_count)s were updated in %(duration).1f seconds",
                    create_count=template_create_count, update_count=template_update_count, duration=sync_duration),
                'next': {'type': 'ir.actions.act_window_close'},
            }
        }

    def button_test_connection(self):
        """ Test connection of the WhatsApp Business Account with performance tracking.
        """
        self.ensure_one()
        test_start_time = fields.Datetime.now()

        wa_api = WhatsAppApi(self)
        try:
            # Increment API counter for monitoring
            self._increment_api_counter()

            wa_api._test_connection()

            # Update last successful connection test
            self.write({'last_connection_test': test_start_time})

            test_duration = (fields.Datetime.now() - test_start_time).total_seconds()
            _logger.info("Connection test successful for account %s in %.2f seconds", self.name, test_duration)

        except WhatsAppError as e:
            # Show more detailed error information
            error_msg = str(e)
            test_duration = (fields.Datetime.now() - test_start_time).total_seconds()
            _logger.error("Connection test failed for account %s after %.2f seconds: %s", self.name, test_duration, error_msg)
            raise UserError(_("Connection test failed:\n\n%s\n\nPlease check:\n1. Your Bearer token is correct\n2. Your token has WhatsApp service enabled\n3. Your account has sufficient balance") % error_msg)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'type': 'success',
                'message': _("Connection successful! Your Taqnyat.sa Bearer token is working correctly.\nResponse time: %.2f seconds") % test_duration,
            }
        }

    def button_debug_connection(self):
        """ Debug connection issues with detailed information """
        self.ensure_one()
        if not self.token:
            raise UserError(_("Please enter your Bearer token first."))

        debug_info = []
        debug_info.append(f"Account: {self.name}")
        debug_info.append(f"Bearer Token: {self.token[:10]}...{self.token[-5:] if len(self.token) > 15 else 'TOO_SHORT'}")
        debug_info.append(f"Token Length: {len(self.token)} characters")
        debug_info.append(f"API Base URL: https://api.taqnyat.sa/wa/v2")

        # Test basic connectivity with correct authentication format
        try:
            import requests
            # Handle both token formats consistently with main API
            if self.token.startswith('Bearer '):
                headers = {'Authorization': self.token}
            else:
                headers = {'Authorization': f'Bearer {self.token}'}

            # Test templates endpoint (the only one available according to Postman collection)
            test_url = "https://api.taqnyat.sa/wa/v2/templates/"
            debug_info.append(f"\n--- Testing Templates Endpoint ---")
            debug_info.append(f"URL: {test_url}")
            debug_info.append(f"Auth Header: Authorization: {self.token[:10]}...{self.token[-5:]}")

            try:
                response = requests.get(test_url, headers=headers, timeout=10)
                debug_info.append(f"Status Code: {response.status_code}")
                debug_info.append(f"Content-Type: {response.headers.get('Content-Type', 'Not specified')}")
                debug_info.append(f"Response (first 300 chars): {response.text[:300]}")

                if response.status_code == 401:
                    debug_info.append(f"❌ Authentication Error: Invalid token")
                elif response.status_code == 403:
                    debug_info.append(f"❌ Access Forbidden: Token may not have WhatsApp service enabled")
                elif response.status_code == 200:
                    if 'application/json' in response.headers.get('Content-Type', ''):
                        debug_info.append(f"✅ JSON Response - API is working correctly!")
                        try:
                            json_data = response.json()
                            debug_info.append(f"✅ Valid JSON structure received")
                            if 'data' in json_data:
                                debug_info.append(f"✅ Templates data found: {len(json_data.get('data', []))} templates")
                        except:
                            debug_info.append(f"⚠️ JSON parsing failed")
                    else:
                        debug_info.append(f"❌ HTML Response - API access not activated")
                        debug_info.append(f"This means your token is valid but API access is not enabled")
                else:
                    debug_info.append(f"⚠️ Unexpected status {response.status_code}")

            except requests.exceptions.RequestException as e:
                debug_info.append(f"❌ Request failed: {str(e)}")

            debug_info.append(f"\n--- Recommendations ---")
            if response.status_code == 200 and 'application/json' in response.headers.get('Content-Type', ''):
                debug_info.append(f"✅ Your API is working correctly!")
                debug_info.append(f"You can now create and manage WhatsApp templates.")
            else:
                debug_info.append(f"❌ API access needs activation:")
                debug_info.append(f"1. Contact Taqnyat.sa <NAME_EMAIL>")
                debug_info.append(f"2. Request: 'Please activate WhatsApp API access'")
                debug_info.append(f"3. Provide your token: {self.token[:10]}...{self.token[-5:]}")
                debug_info.append(f"4. Ensure token is created for WhatsApp service only")

        except Exception as e:
            debug_info.append(f"\n--- Connection Error ---")
            debug_info.append(f"Error: {str(e)}")

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'type': 'info',
                'title': 'Connection Debug Information',
                'message': '\n'.join(debug_info),
                'sticky': True,
            }
        }

    def button_check_api_access(self):
        """ Check if API access is fully functional for template operations """
        self.ensure_one()
        if not self.token:
            raise UserError(_("Please enter your Bearer token first."))

        wa_api = WhatsAppApi(self)
        issues = []

        try:
            # Test 1: Basic connectivity
            wa_api._test_connection()
            issues.append("✅ Basic API connectivity: Working")
        except WhatsAppError as e:
            issues.append(f"❌ Basic API connectivity: {str(e)}")

        try:
            # Test 2: Template listing (read access)
            response = wa_api._get_all_template(fetch_all=False)
            if isinstance(response, dict):
                issues.append("✅ Template API access: Working")
            else:
                issues.append("⚠️ Template API access: Unexpected response format")
        except WhatsAppError as e:
            issues.append(f"❌ Template API access: {str(e)}")

    def button_run_comprehensive_diagnostic(self):
        """ Run comprehensive diagnostic for template submission issues """
        self.ensure_one()
        if not self.token:
            raise UserError(_("Please enter your Bearer token first."))

        try:
            # Import the diagnostic functions directly
            results = self._run_diagnostic_tests()
            formatted_results = self._format_diagnostic_results(results)

            # Log the results for technical review
            _logger.info("WhatsApp diagnostic results for account %s:\n%s", self.name, formatted_results)

            # Show results to user
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'success' if results['success'] else 'warning',
                    'title': _("WhatsApp API Diagnostic Results"),
                    'message': formatted_results,
                    'sticky': True,
                }
            }

        except Exception as e:
            _logger.error("Error running WhatsApp diagnostic: %s", str(e))
            raise UserError(_("Error running diagnostic: %s") % str(e))

    def _run_diagnostic_tests(self):
        """Run comprehensive diagnostic tests for WhatsApp API"""
        from datetime import datetime
        import json

        results = {
            'timestamp': datetime.now().isoformat(),
            'account_name': self.name,
            'token_length': len(self.token) if self.token else 0,
            'tests': {},
            'recommendations': [],
            'success': False
        }

        if not self.token:
            results['recommendations'].append("❌ No Bearer token configured. Please add your Taqnyat.sa Bearer token.")
            return results

        if len(self.token) < 20:
            results['recommendations'].append("⚠️ Bearer token appears too short. Verify it's complete.")

        # Test 1: Basic API connectivity
        try:
            wa_api = WhatsAppApi(self)
            wa_api._test_connection()
            results['tests']['connectivity'] = {'status': 'success', 'message': 'API connectivity successful'}
        except WhatsAppError as e:
            error_msg = str(e)
            results['tests']['connectivity'] = {'status': 'failed', 'message': error_msg}

            if "Authentication failed" in error_msg:
                results['recommendations'].append("❌ Authentication failed - verify your Bearer token")
            elif "Access forbidden" in error_msg:
                results['recommendations'].append("❌ API access forbidden - contact Taqnyat.sa to enable API access")
            elif "Network error" in error_msg or "Connection error" in error_msg:
                results['recommendations'].append("❌ Network connectivity issue - check internet connection and firewall")
            else:
                results['recommendations'].append(f"❌ API connectivity failed: {error_msg}")
            return results
        except Exception as e:
            results['tests']['connectivity'] = {'status': 'error', 'message': f"Unexpected error: {str(e)}"}
            results['recommendations'].append(f"❌ Unexpected connectivity error: {str(e)}")
            return results

        # Test 2: Template listing (read permissions)
        try:
            response = wa_api._get_all_template(fetch_all=False)
            template_count = len(response.get('data', [])) if isinstance(response, dict) else 0
            results['tests']['template_read'] = {
                'status': 'success',
                'message': f'Template read access successful - found {template_count} templates'
            }
        except WhatsAppError as e:
            results['tests']['template_read'] = {'status': 'failed', 'message': str(e)}
            results['recommendations'].append("⚠️ Template read access failed - may indicate API permission issues")
        except Exception as e:
            results['tests']['template_read'] = {'status': 'error', 'message': f"Unexpected error: {str(e)}"}

        # Test 3: Simple template creation test
        test_template_data = {
            'name': f'odoo_diagnostic_test_{int(datetime.now().timestamp())}',
            'language': 'en',
            'allow_category_change': True,
            'category': 'UTILITY',
            'components': [
                {
                    'type': 'BODY',
                    'text': 'This is a diagnostic test message from Odoo'
                }
            ]
        }

        try:
            json_data = json.dumps(test_template_data)
            response = wa_api._submit_template_new(json_data)
            results['tests']['template_creation'] = {
                'status': 'success',
                'message': f'Template creation successful - ID: {response.get("id", "unknown")}'
            }
            results['success'] = True
            results['recommendations'].append("✅ Template creation works - the issue may be with specific template content")

            # Clean up test template if possible
            try:
                if response.get('id'):
                    wa_api._delete_template(response['id'])
                    results['tests']['cleanup'] = {'status': 'success', 'message': 'Test template cleaned up'}
            except:
                results['tests']['cleanup'] = {'status': 'failed', 'message': 'Could not clean up test template'}

        except WhatsAppError as e:
            error_msg = str(e)
            results['tests']['template_creation'] = {'status': 'failed', 'message': error_msg}

            if "Authentication failed" in error_msg:
                results['recommendations'].append("❌ Template creation failed - authentication issue")
            elif "Access forbidden" in error_msg or "permission" in error_msg.lower():
                results['recommendations'].append("❌ Template creation failed - insufficient permissions. Contact Taqnyat.sa to enable template creation API access")
            elif "Network error" in error_msg:
                results['recommendations'].append("❌ Template creation failed - network issue")
            else:
                results['recommendations'].append(f"❌ Template creation failed: {error_msg}")
        except Exception as e:
            results['tests']['template_creation'] = {'status': 'error', 'message': f"Unexpected error: {str(e)}"}
            results['recommendations'].append(f"❌ Unexpected template creation error: {str(e)}")

        # Final recommendations
        if results['success']:
            results['recommendations'].append("🎯 API is working correctly. Check specific template content or format.")
        else:
            results['recommendations'].append("📞 Contact Taqnyat.sa support with these diagnostic results")

        return results

    def _format_diagnostic_results(self, results):
        """Format diagnostic results for display"""
        output = []
        output.append("🔧 WHATSAPP API DIAGNOSTIC RESULTS")
        output.append("=" * 50)
        output.append(f"Timestamp: {results['timestamp']}")
        output.append(f"Account: {results['account_name']}")
        output.append(f"Token Length: {results['token_length']} characters")
        output.append("")

        output.append("📋 TEST RESULTS:")
        for test_name, test_result in results['tests'].items():
            status_icon = "✅" if test_result['status'] == 'success' else "❌" if test_result['status'] == 'failed' else "⚠️"
            output.append(f"{status_icon} {test_name.title()}: {test_result['message']}")

        output.append("")
        output.append("🔧 RECOMMENDATIONS:")
        for rec in results['recommendations']:
            output.append(f"  {rec}")

        return "\n".join(output)

    def action_open_templates(self):
        self.ensure_one()
        return {
            'name': _("Templates Of %(account_name)s", account_name=self.name),
            'view_mode': 'list,form',
            'res_model': 'whatsapp.template',
            'domain': [('wa_account_id', '=', self.id)],
            'type': 'ir.actions.act_window',
            'context': {'default_wa_account_id': self.id},
        }

    def _find_active_channel(self, sender_mobile_formatted, sender_name=False, create_if_not_found=False):
        """This method will find the active channel for the given sender mobile number."""
        self.ensure_one()
        whatsapp_message = self.env['whatsapp.message'].sudo().search(
            [
                ('mobile_number_formatted', '=', sender_mobile_formatted),
                ('wa_account_id', '=', self.id),
                ('wa_template_id', '!=', False),
                ('state', 'not in', ['outgoing', 'error', 'cancel']),
            ], limit=1, order='id desc')
        return self.env['discuss.channel'].sudo()._get_whatsapp_channel(
            whatsapp_number=sender_mobile_formatted,
            wa_account_id=self,
            sender_name=sender_name,
            create_if_not_found=create_if_not_found,
            related_message=whatsapp_message.mail_message_id,
        )

    def _process_messages(self, value):
        """
            This method is used for processing messages with the values received via webhook.
            If any whatsapp message template has been sent from this account then it will find the active channel or
            create new channel with last template message sent to that number and post message in that channel.
            And if channel is not found then it will create new channel with notify user set in account and post message.
            Supported Messages
             => Text Message
             => Attachment Message with caption
             => Location Message
             => Contact Message
             => Message Reactions
        """
        if 'messages' not in value and value.get('whatsapp_business_api_data', {}).get('messages'):
            value = value['whatsapp_business_api_data']

        wa_api = WhatsAppApi(self)

        for messages in value.get('messages', []):
            parent_msg_id = False
            parent_id = False
            channel = False
            sender_name = value.get('contacts', [{}])[0].get('profile', {}).get('name')
            sender_mobile = messages['from']
            message_type = messages['type']
            if 'context' in messages and messages['context'].get('id'):
                parent_whatsapp_message = self.env['whatsapp.message'].sudo().search([('msg_uid', '=', messages['context']['id'])])
                if parent_whatsapp_message:
                    parent_msg_id = parent_whatsapp_message.id
                    parent_id = parent_whatsapp_message.mail_message_id
                if parent_id:
                    channel = self.env['discuss.channel'].sudo().search([('message_ids', 'in', parent_id.id)], limit=1)

            if not channel:
                channel = self._find_active_channel(sender_mobile, sender_name=sender_name, create_if_not_found=True)
            kwargs = {
                'message_type': 'whatsapp_message',
                'author_id': channel.whatsapp_partner_id.id,
                'parent_msg_id': parent_msg_id,
                'subtype_xmlid': 'mail.mt_comment',
                'parent_id': parent_id.id if parent_id else None
            }
            if message_type == 'text':
                kwargs['body'] = plaintext2html(messages['text']['body'])
            elif message_type == 'button':
                kwargs['body'] = messages['button']['text']
            elif message_type in ('document', 'image', 'audio', 'video', 'sticker'):
                filename = messages[message_type].get('filename')
                mime_type = messages[message_type].get('mime_type')
                caption = messages[message_type].get('caption')
                datas = wa_api._get_whatsapp_document(messages[message_type]['id'])
                if not filename:
                    extension = mimetypes.guess_extension(mime_type) or ''
                    filename = message_type + extension
                kwargs['attachments'] = [(filename, datas)]
                if caption:
                    kwargs['body'] = plaintext2html(caption)
            elif message_type == 'location':
                url = Markup("https://maps.google.com/maps?q={latitude},{longitude}").format(
                    latitude=messages['location']['latitude'], longitude=messages['location']['longitude'])
                body = Markup('<a target="_blank" href="{url}"> <i class="fa fa-map-marker"/> {location_string} </a>').format(
                    url=url, location_string=_("Location"))
                if messages['location'].get('name'):
                    body += Markup("<br/>{location_name}").format(location_name=messages['location']['name'])
                if messages['location'].get('address'):
                    body += Markup("<br/>{location_address}").format(location_name=messages['location']['address'])
                kwargs['body'] = body
            elif message_type == 'contacts':
                body = ""
                for contact in messages['contacts']:
                    body += Markup("<i class='fa fa-address-book'/> {contact_name} <br/>").format(
                        contact_name=contact.get('name', {}).get('formatted_name', ''))
                    for phone in contact.get('phones'):
                        body += Markup("{phone_type}: {phone_number}<br/>").format(
                            phone_type=phone.get('type'), phone_number=phone.get('phone'))
                kwargs['body'] = body
            elif message_type == 'reaction':
                msg_uid = messages['reaction'].get('message_id')
                whatsapp_message = self.env['whatsapp.message'].sudo().search([('msg_uid', '=', msg_uid)])
                if whatsapp_message:
                    partner_id = channel.whatsapp_partner_id
                    emoji = messages['reaction'].get('emoji')
                    whatsapp_message.mail_message_id._post_whatsapp_reaction(reaction_content=emoji, partner_id=partner_id)
                    continue
            else:
                _logger.warning("Unsupported whatsapp message type: %s", messages)
                continue
            channel.message_post(whatsapp_inbound_msg_uid=messages['id'], **kwargs)

    def _process_taqnyat_message(self, notification):
        """
        Process Taqnyat.sa inbound message format

        Taqnyat.sa notification structure:
        {
            "from": "966xxxxxxxxx",
            "to": "bot_identifier",
            "message_id": "generated_id",
            "message": {...},
            "timestamp": "ISO-8601",
            "forwarded": boolean,
            "frequently_forwarded": boolean
        }
        """
        sender_mobile = notification.get('from')
        message_data = notification.get('message', {})
        message_type = message_data.get('type', 'text')
        message_id = notification.get('message_id')

        if not sender_mobile or not message_id:
            _logger.warning("Invalid Taqnyat notification format: %s", notification)
            return

        # Find or create channel
        channel = self._find_active_channel(sender_mobile, create_if_not_found=True)

        kwargs = {
            'message_type': 'whatsapp_message',
            'author_id': channel.whatsapp_partner_id.id,
            'subtype_xmlid': 'mail.mt_comment',
        }

        # Process different message types
        if message_type == 'text':
            kwargs['body'] = plaintext2html(message_data.get('body', ''))
        elif message_type in ('image', 'video', 'document', 'audio'):
            # For Taqnyat.sa, we might need to download media differently
            media_data = message_data.get(message_type, {})
            caption = media_data.get('caption')
            if caption:
                kwargs['body'] = plaintext2html(caption)
            # Note: Media handling for Taqnyat.sa might need adjustment based on their API
        elif message_type == 'location':
            location = message_data.get('location', {})
            lat = location.get('latitude')
            lng = location.get('longitude')
            if lat and lng:
                url = Markup("https://maps.google.com/maps?q={},{}").format(lat, lng)
                body = Markup('<a target="_blank" href="{}"> <i class="fa fa-map-marker"/> {} </a>').format(
                    url, _("Location"))
                kwargs['body'] = body
        else:
            _logger.warning("Unsupported Taqnyat message type: %s", message_type)
            return

        channel.message_post(whatsapp_inbound_msg_uid=message_id, **kwargs)

    @api.model
    def is_whatsapp_available(self):
        """Check if WhatsApp is available for the current company"""
        accounts = self.search([
            ('allowed_company_ids', 'in', self.env.company.ids)
        ], limit=1)

        if not accounts:
            return False

        # Check if at least one account has proper authentication
        for account in accounts:
            has_taqnyat = bool(account.token)
            has_meta = bool(account.app_uid and account.app_secret and account.account_uid and account.phone_uid)

            if has_taqnyat or has_meta:
                return True

        return False
