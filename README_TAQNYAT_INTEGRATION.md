# Taqnyat WhatsApp Business API Integration

## Overview

This enhanced WhatsApp module provides comprehensive integration with the Taqnyat.sa WhatsApp Business API v2, offering advanced features for message management, session tracking, opt-in management, and webhook handling.

## Features

### 🚀 Core Features
- **Complete Taqnyat API Integration** - Full support for all Taqnyat WhatsApp Business API v2 endpoints
- **Smart Message Routing** - Automatic detection of 24-hour session windows for template vs conversation messages
- **Enhanced Message Types** - Support for text, media, interactive (buttons/lists), location, and contact messages
- **Comprehensive Template Management** - Create, manage, and send template messages with dynamic content
- **Advanced Opt-in Management** - GDPR-compliant consent management with bulk operations
- **International Phone Validation** - Support for multiple country formats with automatic detection
- **Real-time Webhook Processing** - Handle delivery reports, inbound messages, and status updates
- **Session Window Tracking** - Automatic 24-hour session management for compliance
- **Media Upload & Management** - Support for images, videos, documents, and audio files
- **Error Handling & Retry Logic** - Robust error handling with exponential backoff

### 📱 Message Types Supported
- **Text Messages** - Simple text with URL preview support
- **Template Messages** - Pre-approved templates with dynamic parameters
- **Interactive Messages** - Buttons and list selections
- **Media Messages** - Images, videos, documents, audio
- **Location Messages** - Share location coordinates
- **Contact Messages** - Share contact information

### 🌍 International Support
- **Multi-country Phone Validation** - Support for Saudi Arabia, UAE, Egypt, Jordan, Kuwait, Qatar, and more
- **Automatic Country Detection** - Detect country from phone number format
- **Localized Templates** - Multi-language template support

## Installation & Setup

### 1. Prerequisites
- Odoo 15.0 or higher
- Active Taqnyat.sa WhatsApp Business API account
- Valid API token from Taqnyat

### 2. Configuration
1. Navigate to **WhatsApp > Configuration > Accounts**
2. Create a new WhatsApp account with:
   - **Name**: Your account identifier
   - **Token**: Your Taqnyat API token (format: `Bearer your_token_here`)
   - **Phone UID**: Your WhatsApp Business phone number
   - **Account UID**: Your Taqnyat account identifier

### 3. Webhook Setup
Configure your webhook URL in Taqnyat dashboard:
```
https://your-odoo-domain.com/whatsapp/webhook
```

## Usage Examples

### Basic Message Sending
```python
# Get WhatsApp API instance
from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi

wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
api = WhatsAppApi(wa_account)

# Send smart message (automatically chooses template vs conversation)
result = api.send_message_smart(
    phone_number="************",
    message_type='text',
    content="Hello! How can I help you today?",
    preview_url=True
)
```

### Interactive Button Message
```python
from odoo.addons.whatsapp.tools.message_builder import TaqnyatMessageBuilder

buttons = [
    {"reply": {"id": "yes", "title": "Yes"}},
    {"reply": {"id": "no", "title": "No"}}
]

message = TaqnyatMessageBuilder.build_interactive_button_message(
    body_text="Would you like to receive updates?",
    buttons=buttons,
    header={"type": "text", "text": "Notifications"},
    footer="Choose your preference"
)

result = api.send_message_smart(
    phone_number="************",
    message_type='interactive',
    content=message
)
```

### Opt-in Management
```python
from odoo.addons.whatsapp.tools.phone_validator import OptInManager

manager = OptInManager(env)

# Record opt-in
manager.record_opt_in("************", wa_account.id, 'website', True)

# Check if can send marketing messages
can_send, reason = manager.can_send_message("************", wa_account.id, 'marketing')

# Bulk opt-in
phone_list = ["************", "************"]
result = manager.bulk_opt_in(phone_list, wa_account.id, 'bulk_import')
```

### Template Management
```python
from odoo.addons.whatsapp.tools.message_builder import TaqnyatTemplateBuilder

# Create template
template_data = TaqnyatTemplateBuilder.build_text_template(
    name="order_confirmation",
    category="utility",
    language="ar",
    body_text="تم تأكيد طلبك رقم {{1}}. سيتم التوصيل خلال {{2}} أيام.",
    header_text="تأكيد الطلب"
)

result = api.create_template_enhanced(template_data)
```

## API Reference

### Core Classes

#### WhatsAppApi
Main API interface for sending messages and managing WhatsApp operations.

**Key Methods:**
- `send_message_smart()` - Smart message sending with session detection
- `upload_media_enhanced()` - Upload media files
- `get_session_status()` - Check 24-hour session window status
- `get_templates_enhanced()` - Retrieve templates with pagination

#### TaqnyatMessageBuilder
Utility class for building different message types.

**Key Methods:**
- `build_text_message()` - Create text message payload
- `build_template_message()` - Create template message payload
- `build_interactive_button_message()` - Create button message payload
- `build_interactive_list_message()` - Create list message payload

#### PhoneNumberValidator
Phone number validation and formatting utilities.

**Key Methods:**
- `validate_phone_format()` - Validate international phone format
- `clean_phone_number()` - Clean and normalize phone number
- `detect_country()` - Detect country from phone number

#### OptInManager
Manage opt-in/opt-out consent for contacts.

**Key Methods:**
- `record_opt_in()` - Record consent for marketing messages
- `record_opt_out()` - Record opt-out request
- `can_send_message()` - Check sending permissions
- `bulk_opt_in()` - Bulk consent management

#### SessionWindowManager
Manage 24-hour session windows for message type determination.

**Key Methods:**
- `is_session_active()` - Check if session window is active
- `get_message_type_recommendation()` - Get recommended message type
- `update_last_inbound_message()` - Update session tracking

## Configuration Options

### Message Settings
- **Session Window Duration**: 24 hours (WhatsApp standard)
- **Retry Attempts**: 3 attempts with exponential backoff
- **Media Size Limits**: As per Taqnyat specifications
- **Template Categories**: Marketing, Utility, Authentication

### Validation Settings
- **Phone Number Formats**: International format without + prefix
- **Supported Countries**: 18+ Middle East and North Africa countries
- **Opt-in Sources**: Manual, Website, API, Bulk Import, QR Code

### Webhook Events
- **Message Status Updates**: Queued, Sent, Delivered, Read, Failed
- **Inbound Messages**: Text, Media, Interactive responses
- **Template Status**: Approved, Rejected, Pending

## Error Handling

The integration includes comprehensive error handling:

### Retryable Errors (with exponential backoff)
- **429**: Rate limiting
- **500+**: Server errors
- **Network timeouts**

### Non-retryable Errors
- **400**: Bad request (invalid data)
- **401**: Authentication failed
- **403**: Forbidden (insufficient permissions)
- **404**: Resource not found

### Error Logging
All errors are logged with detailed context for debugging and monitoring.

## Testing

### Running Tests
```bash
# Run all integration tests
python -m pytest whatsapp/tests/test_taqnyat_integration.py

# Run specific test class
python -m pytest whatsapp/tests/test_taqnyat_integration.py::TestPhoneNumberValidation
```

### Test Coverage
- Phone number validation
- Opt-in/opt-out management
- Session window tracking
- Message building
- API integration (mocked)

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify API token format: `Bearer your_token_here`
   - Check token validity in Taqnyat dashboard

2. **Phone Number Validation Errors**
   - Ensure international format without + prefix
   - Check supported country patterns

3. **Template Message Failures**
   - Verify template is approved in Taqnyat
   - Check parameter count and types

4. **Session Window Issues**
   - Confirm 24-hour window calculation
   - Check last inbound message tracking

### Debug Mode
Enable debug logging in Odoo configuration:
```ini
[logger_whatsapp]
level = DEBUG
handlers = console
qualname = odoo.addons.whatsapp
```

## Support & Documentation

- **Taqnyat API Documentation**: https://api.taqnyat.sa/docs
- **WhatsApp Business API**: https://developers.facebook.com/docs/whatsapp
- **Odoo Documentation**: https://www.odoo.com/documentation

## License

This module is part of Odoo. See LICENSE file for full copyright and licensing details.

---

**Version**: 1.0.0  
**Last Updated**: 2024-06-24  
**Compatibility**: Odoo 15.0+  
**API Version**: Taqnyat WhatsApp Business API v2
