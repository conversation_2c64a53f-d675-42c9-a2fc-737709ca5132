<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="whatsapp_account_view_tree" model="ir.ui.view">
        <field name="name">whatsapp.account.view.list</field>
        <field name="model">whatsapp.account</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="token" password="True"/>
                <!-- Temporarily disabled for upgrade -->
                <!-- <field name="last_sync_date" optional="show"/>
                <field name="last_connection_test" optional="show"/>
                <field name="api_call_count" optional="hide"/> -->
                <field name="phone_uid" optional="hide"/>
                <field name="account_uid" optional="hide"/>
                <field name="app_uid" optional="hide"/>
                <field name="allowed_company_ids" widget="many2many_tags" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>
    <record id="whatsapp_account_view_form" model="ir.ui.view">
        <field name="name">whatsapp.account.view.form</field>
        <field name="model">whatsapp.account</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Business Account">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object" name="action_open_templates" class="oe_stat_button" icon="fa-whatsapp">
                            <field name="templates_count" string="Templates" widget="statinfo"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_title" id='title'>
                        <label for="name" string="Name"/>
                        <h1 id="name"><field name="name" required="1" placeholder='e.g. "Acme Inc. Business Account"'/></h1>
                    </div>
                    <field name="active" invisible="1"/>

                    <!-- Primary Taqnyat.sa Configuration -->
                    <div class="alert alert-info" role="alert">
                        <strong><i class="fa fa-info-circle"/> Recommended Setup</strong><br/>
                        Use Taqnyat.sa WhatsApp Business API for the best experience with simplified authentication,
                        better regional support, and competitive pricing.
                    </div>

                    <div class="o_horizontal_separator mt-4 mb-3 text-uppercase fw-bolder small text-primary">
                        <i class="fa fa-whatsapp text-success"/> Taqnyat.sa WhatsApp Business API
                        <widget name="documentation_link" path="https://dev.taqnyat.sa/ar/doc/whatsapp/#api-ref" icon="fa-external-link"/>
                    </div>

                    <div class="row mt16 o_settings_container" id="taqnyat_primary_setup">
                        <div class="col-12">
                            <div class="o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="token" password="True"
                                           placeholder="e.g. 8d18d2f9045b7145ff81***********"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="token" class="o_form_label">
                                        <span class="text-primary"><strong>Bearer Token</strong></span>
                                        <small class="text-muted">(Recommended)</small>
                                    </label>
                                    <div class="text-muted mb-2">
                                        <i class="fa fa-key text-primary"/> <strong>Primary authentication method for Taqnyat.sa API</strong><br/>
                                        Get your Bearer token from <a href="https://portal.taqnyat.sa" target="_blank" class="btn-link p-0">
                                            <i class="fa fa-external-link"/> Taqnyat.sa Portal
                                        </a> → Developer → Applications → WhatsApp Service
                                    </div>
                                    <div class="mt-2">
                                        <button name="button_test_connection" type="object" string="Test Connection"
                                                icon="fa-plug" class="btn btn-primary btn-sm me-2"
                                                invisible="not token"/>
                                        <button name="button_debug_connection" type="object" string="Debug"
                                                icon="fa-bug" class="btn btn-info btn-sm me-2"
                                                invisible="not token"/>
                                        <button name="button_check_api_access" type="object" string="Check API Access"
                                                icon="fa-shield" class="btn btn-warning btn-sm me-2"
                                                invisible="not token"/>
                                        <button name="button_run_comprehensive_diagnostic" type="object" string="Run Diagnostic"
                                                icon="fa-stethoscope" class="btn btn-danger btn-sm me-2"
                                                invisible="not token"
                                                help="Run comprehensive diagnostic for template submission issues"/>
                                        <button name="button_sync_whatsapp_account_templates" type="object"
                                                string="Sync Templates" icon="fa-refresh" class="btn btn-secondary btn-sm"
                                                invisible="not token"/>
                                    </div>
                                    <div class="mt-2" invisible="token">
                                        <div class="alert alert-warning" role="alert">
                                            <i class="fa fa-exclamation-triangle"/> Please enter your Bearer token to enable API functionality
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Collapsible Legacy Meta API Section -->
                    <div class="o_horizontal_separator mt-5 mb-3 text-uppercase fw-bolder small text-muted">
                        <i class="fa fa-archive"/> Legacy Meta API Configuration (Optional)
                        <small class="text-lowercase fw-normal"> - For existing Meta API integrations only</small>
                    </div>

                    <div class="row mt16 o_settings_container" id="legacy_meta_setup">
                        <div class="col-12">
                            <div class="alert alert-warning" role="alert">
                                <i class="fa fa-exclamation-triangle"/> <strong>Legacy Configuration</strong><br/>
                                These fields are for backward compatibility with existing Meta WhatsApp Business API setups.
                                For new configurations, use the Taqnyat.sa Bearer token above.
                            </div>
                        </div>

                        <setting string="App ID" help="Meta WhatsApp Business App ID (Legacy)">
                            <field name="app_uid" placeholder="e.g. ***************"
                                   readonly="token"/>
                        </setting>

                        <setting string="App Secret" help="Meta WhatsApp Business App Secret (Legacy)">
                            <field name="app_secret" password="True"
                                   placeholder="e.g. 00112233445566778899aabbccddeeff"
                                   readonly="token"/>
                        </setting>

                        <setting string="Phone Number ID" help="Meta WhatsApp Business Phone Number ID (Legacy)">
                            <field name="phone_uid" placeholder="e.g. ***************"
                                   readonly="token"/>
                        </setting>

                        <setting string="Business Account ID" help="Meta WhatsApp Business Account ID (Legacy)">
                            <field name="account_uid" placeholder="e.g. ***************"
                                   readonly="token"/>
                        </setting>
                    </div>
                    <div class="o_horizontal_separator mt-5 mb-3 text-uppercase fw-bolder small">
                        <i class="fa fa-comments"/> Receiving Messages &amp; Webhooks
                        <widget name="documentation_link" path="https://dev.taqnyat.sa/ar/doc/whatsapp/#call-back" icon="fa-external-link"/>
                    </div>

                    <div class="row mt16 o_settings_container" id="webhook_config">
                        <div class="col-12">
                            <div class="alert alert-info" role="alert">
                                <i class="fa fa-info-circle"/> Configure this webhook URL in your Taqnyat.sa portal to receive message delivery reports and inbound messages.
                            </div>
                        </div>

                        <setting string="Webhook URL" help="Configure this URL in your Taqnyat.sa portal for receiving webhooks">
                            <field name="callback_url" widget="CopyClipboardURL" readonly="1"/>
                            <div class="text-muted mt-1">
                                <small>Copy this URL and configure it in your Taqnyat.sa portal webhook settings</small>
                            </div>
                        </setting>

                        <setting string="Webhook Verify Token" help="Token for webhook verification (Meta API compatibility)">
                            <field name="webhook_verify_token" widget="CopyClipboardChar" readonly="1"/>
                            <div class="text-muted mt-1">
                                <small>Used for Meta API webhook verification (legacy compatibility)</small>
                            </div>
                        </setting>
                    </div>

                    <div class="o_horizontal_separator mt-5 mb-3 text-uppercase fw-bolder small">
                        <i class="fa fa-bell"/> Notifications &amp; Access Control
                    </div>

                    <div class="row mt16 o_settings_container" id="notifications_config">
                        <setting string="Notification Users" help="Users who will be notified when inbound messages are received">
                            <field name="notify_user_ids" widget="many2many_tags"
                                   placeholder="Select users to notify for inbound messages..."
                                   domain="[('share', '=', False)]"/>
                            <div class="text-muted mt-1">
                                <small>These users will be notified when WhatsApp messages are received and no recent template was sent</small>
                            </div>
                        </setting>

                        <setting string="Allowed Companies" help="Companies that can use this WhatsApp account"
                                 groups="base.group_multi_company">
                            <field name="allowed_company_ids" widget="many2many_tags" required="1"
                                   placeholder="Select companies..."/>
                            <div class="text-muted mt-1">
                                <small>Restrict access to this WhatsApp account to specific companies</small>
                            </div>
                        </setting>
                    </div>

                    <!-- Performance Monitoring Section - Temporarily disabled for upgrade -->
                    <!-- Will be enabled after successful upgrade -->
                    <!--
                    <div class="o_horizontal_separator mt-5 mb-3 text-uppercase fw-bolder small">
                        <i class="fa fa-tachometer"/> Performance &amp; Monitoring
                    </div>

                    <div class="row mt16 o_settings_container" id="performance_monitoring">
                        <div class="col-12">
                            <div class="alert alert-info" role="alert">
                                <i class="fa fa-info-circle"/> Monitor API usage and performance metrics for this WhatsApp account.
                            </div>
                        </div>

                        <setting string="Last Template Sync" help="When templates were last synchronized from Taqnyat.sa">
                            <field name="last_sync_date" readonly="1"/>
                            <div class="text-muted mt-1">
                                <small>Templates are automatically synchronized when you click "Sync Templates"</small>
                            </div>
                        </setting>

                        <setting string="Last Connection Test" help="When connection was last successfully tested">
                            <field name="last_connection_test" readonly="1"/>
                            <div class="text-muted mt-1">
                                <small>Connection is tested when you click "Test Connection"</small>
                            </div>
                        </setting>

                        <setting string="API Calls Today" help="Number of API calls made today">
                            <field name="api_call_count" readonly="1"/>
                            <div class="text-muted mt-1">
                                <small>Counter resets daily. Includes template sync, connection tests, and message sending</small>
                            </div>
                        </setting>

                        <setting string="Last API Reset" help="When the API counter was last reset">
                            <field name="last_api_reset" readonly="1"/>
                            <div class="text-muted mt-1">
                                <small>API counters reset daily for monitoring purposes</small>
                            </div>
                        </setting>
                    </div>
                    -->
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>
    <record id="whatsapp_account_view_search" model="ir.ui.view">
        <field name="name">whatsapp.account.view.search</field>
        <field name="model">whatsapp.account</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
            </search>
        </field>
    </record>
    <record id="whatsapp_account_action" model="ir.actions.act_window">
        <field name="name">WhatsApp Business Account</field>
        <field name="res_model">whatsapp.account</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                <i class="fa fa-whatsapp fa-3x text-success mb-3"/>
            </p>
            <p class="lead">
                <strong>No WhatsApp Business Account Configured Yet!</strong>
            </p>
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fa fa-rocket"/> Quick Setup with Taqnyat.sa</h5>
                        </div>
                        <div class="card-body">
                            <ol class="mb-3">
                                <li>Create an account on <a href="https://portal.taqnyat.sa" target="_blank" class="btn-link">
                                    <strong>Taqnyat.sa Portal</strong> <i class="fa fa-external-link"/>
                                </a></li>
                                <li>Navigate to <strong>Developer → Applications</strong></li>
                                <li>Create a new application for <strong>WhatsApp Service</strong></li>
                                <li>Copy your <strong>Bearer Token</strong></li>
                                <li>Click <strong>"New"</strong> above and paste your token</li>
                            </ol>
                            <div class="alert alert-info mb-0">
                                <i class="fa fa-info-circle"/> <strong>Why Taqnyat.sa?</strong><br/>
                                ✓ Simplified setup with just a Bearer token<br/>
                                ✓ Better regional support for Middle East<br/>
                                ✓ Competitive pricing and reliable delivery<br/>
                                ✓ Arabic documentation and local support
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <a href="https://dev.taqnyat.sa/ar/doc/whatsapp/#api-ref" target="_blank" class="btn btn-link">
                            <i class="fa fa-book"/> View API Documentation
                        </a>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>
