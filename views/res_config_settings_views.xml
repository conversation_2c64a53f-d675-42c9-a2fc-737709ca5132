<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.whatsapp</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <setting id="tenor_api_key" position="before">
                <setting id="module_whatsapp" string="WhatsApp" help="Send and receive message through your WhatsApp Business account." documentation="/applications/productivity/whatsapp.html">
                    <div class="mt16">
                        <button name="%(whatsapp.whatsapp_account_action)d" string="Configure Meta Accounts" type="action" class="oe_link" icon="oi-arrow-right"/>
                    </div>

                    <!-- Global Phone Field Integration Settings -->
                    <div class="mt16">
                        <h4>Global Phone Field Integration</h4>
                        <div class="text-muted">
                            <p>Configure global WhatsApp integration for all phone fields across the system.</p>
                            <p><strong>Note:</strong> These settings are controlled via system parameters:</p>
                            <ul>
                                <li><code>whatsapp.global_phone_integration</code> - Enable/disable global integration</li>
                                <li><code>whatsapp.excluded_models</code> - Comma-separated list of excluded models</li>
                                <li><code>whatsapp.availability_cache_duration</code> - Cache duration in milliseconds</li>
                            </ul>
                            <p>Use the <strong>Technical > Parameters > System Parameters</strong> menu to configure these settings.</p>
                        </div>
                    </div>
                </setting>
            </setting>
        </field>
    </record>
</odoo>
