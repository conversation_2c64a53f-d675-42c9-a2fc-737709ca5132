<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Direct Message Form View -->
    <record id="whatsapp_direct_message_view_form" model="ir.ui.view">
        <field name="name">whatsapp.direct.message.form</field>
        <field name="model">whatsapp.direct.message</field>
        <field name="arch" type="xml">
            <form string="Send WhatsApp Message">
                <header>
                    <button name="action_send_message" 
                            string="Send Message" 
                            type="object" 
                            class="btn-primary"
                            invisible="state in ['sending', 'sent']"/>
                    <button name="action_retry" 
                            string="Retry" 
                            type="object" 
                            class="btn-secondary"
                            invisible="state != 'error'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,sending,sent"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <i class="fa fa-whatsapp text-success me-2"/>
                            Send WhatsApp Message
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="wa_account_id" 
                                   options="{'no_create': True, 'no_edit': True}"
                                   readonly="state in ['sending', 'sent']"/>
                            <field name="phone_number" 
                                   placeholder="e.g., +************"
                                   readonly="state in ['sending', 'sent']"/>
                            <field name="partner_id" 
                                   invisible="1"/>
                        </group>
                        <group>
                            <field name="thread_model" invisible="1"/>
                            <field name="thread_id" invisible="1"/>
                            <field name="message_id" readonly="1" invisible="state != 'sent'"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="message_body" 
                               widget="text" 
                               placeholder="Type your WhatsApp message here..."
                               readonly="state in ['sending', 'sent']"
                               nolabel="1"/>
                    </group>
                    
                    <div class="alert alert-danger" invisible="state != 'error'">
                        <strong>Error:</strong>
                        <field name="error_message" readonly="1" nolabel="1"/>
                    </div>
                    
                    <div class="alert alert-success" invisible="state != 'sent'">
                        <i class="fa fa-check-circle me-2"/>
                        <strong>Message sent successfully!</strong>
                        <span invisible="not message_id">
                            Message ID: <field name="message_id" readonly="1" nolabel="1"/>
                        </span>
                    </div>
                    
                    <div class="alert alert-info" invisible="state != 'sending'">
                        <i class="fa fa-spinner fa-spin me-2"/>
                        <strong>Sending message...</strong>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Direct Message Action -->
    <record id="whatsapp_direct_message_action" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.direct.message</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="whatsapp_direct_message_view_form"/>
    </record>

    <!-- Quick Send WhatsApp Message Action (for context menu) -->
    <record id="whatsapp_direct_message_quick_action" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.direct.message</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="whatsapp_direct_message_view_form"/>
        <field name="context">{
            'default_thread_model': active_model,
            'default_thread_id': active_id,
        }</field>
    </record>
</odoo>
