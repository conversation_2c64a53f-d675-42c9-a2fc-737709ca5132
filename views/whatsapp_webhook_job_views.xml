<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="whatsapp_webhook_job_view_tree" model="ir.ui.view">
            <field name="name">whatsapp.webhook.job.tree</field>
            <field name="model">whatsapp.webhook.job</field>
            <field name="arch" type="xml">
                <tree string="WhatsApp Webhook Jobs">
                    <field name="name"/>
                    <field name="webhook_type"/>
                    <field name="state"/>
                    <field name="retry_count"/>
                    <field name="create_date"/>
                    <field name="last_retry_date"/>
                </tree>
            </field>
        </record>

        <record id="whatsapp_webhook_job_view_form" model="ir.ui.view">
            <field name="name">whatsapp.webhook.job.form</field>
            <field name="model">whatsapp.webhook.job</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Webhook Job">
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="webhook_type"/>
                                <field name="state"/>
                            </group>
                            <group>
                                <field name="retry_count"/>
                                <field name="create_date"/>
                                <field name="last_retry_date"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Webhook Data">
                                <field name="webhook_data" widget="json_editor"/>
                            </page>
                            <page string="Failure Reason" attrs="{'invisible': [('state', '!=', 'failed')]}">
                                <field name="failure_reason"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="whatsapp_webhook_job_action" model="ir.actions.act_window">
            <field name="name">WhatsApp Webhook Jobs</field>
            <field name="res_model">whatsapp.webhook.job</field>
            <field name="view_mode">tree,form</field>
        </record>

        <menuitem id="whatsapp_webhook_job_menu" 
                  name="Webhook Jobs" 
                  parent="whatsapp.whatsapp_menu_configuration" 
                  action="whatsapp_webhook_job_action" 
                  sequence="10"/>

    </data>
</odoo>

