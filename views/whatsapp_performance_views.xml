<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- WhatsApp Performance Views - Simplified for Odoo 18 compatibility -->

    <!-- WhatsApp Message Performance Graph -->
    <record id="whatsapp_message_performance_graph" model="ir.ui.view">
        <field name="name">WhatsApp Message Performance Graph</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <graph string="Message Performance" type="bar" sample="1">
                <field name="create_date" interval="day"/>
                <field name="state"/>
                <field name="send_duration" type="measure"/>
                <field name="delivery_duration" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- WhatsApp Message Performance Pivot -->
    <record id="whatsapp_message_performance_pivot" model="ir.ui.view">
        <field name="name">WhatsApp Message Performance Pivot</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <pivot string="Message Performance Analysis" sample="1">
                <field name="wa_account_id" type="row"/>
                <field name="state" type="col"/>
                <field name="send_duration" type="measure"/>
                <field name="delivery_duration" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Enhanced WhatsApp Message List with Performance -->
    <record id="whatsapp_message_performance_list" model="ir.ui.view">
        <field name="name">WhatsApp Message Performance List</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <list create="0" string="Message Performance">
                <field name="create_date"/>
                <field name="wa_account_id"/>
                <field name="mobile_number" string="Recipient"/>
                <field name="wa_template_id"/>
                <field name="state" 
                       decoration-info="state == 'sent'"
                       decoration-success="state in ['delivered', 'read']"
                       decoration-danger="state in ['error', 'cancel']"
                       widget="badge"/>
                <field name="send_duration" string="Send Time (s)" 
                       decoration-danger="send_duration > 5"
                       decoration-warning="send_duration > 2"/>
                <field name="delivery_duration" string="Delivery Time (s)"
                       decoration-danger="delivery_duration > 30"
                       decoration-warning="delivery_duration > 10"/>
                <field name="failure_reason" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- WhatsApp Account Performance List -->
    <record id="whatsapp_account_performance_list" model="ir.ui.view">
        <field name="name">WhatsApp Account Performance List</field>
        <field name="model">whatsapp.account</field>
        <field name="arch" type="xml">
            <list create="0" string="Account Performance">
                <field name="name"/>
                <field name="last_sync_date"/>
                <field name="last_connection_test"/>
                <field name="api_call_count" 
                       decoration-warning="api_call_count > 100"
                       decoration-danger="api_call_count > 500"/>
                <field name="templates_count"/>
                <button name="button_test_connection" string="Test" type="object" 
                        icon="fa-plug" class="btn-primary"/>
                <button name="button_sync_whatsapp_account_templates" string="Sync" type="object" 
                        icon="fa-refresh" class="btn-secondary"/>
            </list>
        </field>
    </record>

    <!-- Performance Search View -->
    <record id="whatsapp_message_performance_search" model="ir.ui.view">
        <field name="name">WhatsApp Message Performance Search</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <search>
                <field name="wa_account_id"/>
                <field name="mobile_number"/>
                <field name="state"/>
                <field name="wa_template_id"/>
                
                <!-- Performance Filters -->
                <filter string="Slow Send (>2s)" name="slow_send"
                        domain="[('send_duration', '>', 2)]"/>
                <filter string="Very Slow Send (>5s)" name="very_slow_send"
                        domain="[('send_duration', '>', 5)]"/>
                <filter string="Slow Delivery (>10s)" name="slow_delivery"
                        domain="[('delivery_duration', '>', 10)]"/>
                <filter string="Very Slow Delivery (>30s)" name="very_slow_delivery"
                        domain="[('delivery_duration', '>', 30)]"/>
                
                <separator/>
                
                <!-- State Filters -->
                <filter string="Successful" name="successful"
                        domain="[('state', 'in', ['sent', 'delivered', 'read'])]"/>
                <filter string="Failed" name="failed"
                        domain="[('state', 'in', ['error', 'cancel'])]"/>
                
                <separator/>
                
                <!-- Time Filters -->
                <filter string="Today" name="today"
                        domain="[('create_date', '>=', context_today())]"/>
                <filter string="Last 7 Days" name="last_week"
                        domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="Last 30 Days" name="last_month"
                        domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                
                <group string="Group By">
                    <filter string="Account" name="group_account"
                            context="{'group_by': 'wa_account_id'}"/>
                    <filter string="State" name="group_state"
                            context="{'group_by': 'state'}"/>
                    <filter string="Template" name="group_template"
                            context="{'group_by': 'wa_template_id'}"/>
                    <filter string="Date" name="group_date"
                            context="{'group_by': 'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="whatsapp_performance_dashboard_action" model="ir.actions.act_window">
        <field name="name">WhatsApp Performance Dashboard</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">graph,pivot,list</field>
        <field name="view_id" ref="whatsapp_message_performance_graph"/>
        <field name="search_view_id" ref="whatsapp_message_performance_search"/>
        <field name="context">{'search_default_last_week': 1, 'search_default_successful': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                <i class="fa fa-tachometer fa-3x text-primary mb-3"/>
            </p>
            <p class="lead">
                <strong>No Performance Data Available</strong>
            </p>
            <p>
                Send some WhatsApp messages to see performance metrics here.
                This dashboard shows:
            </p>
            <ul class="text-start">
                <li>Message send and delivery times</li>
                <li>Success and failure rates</li>
                <li>Account performance comparison</li>
                <li>API usage statistics</li>
            </ul>
        </field>
    </record>

    <record id="whatsapp_account_performance_action" model="ir.actions.act_window">
        <field name="name">Account Performance</field>
        <field name="res_model">whatsapp.account</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="whatsapp_account_performance_list"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                <i class="fa fa-whatsapp fa-3x text-success mb-3"/>
            </p>
            <p class="lead">
                <strong>Monitor WhatsApp Account Performance</strong>
            </p>
            <p>
                Track API usage, sync status, and connection health for all your WhatsApp accounts.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="whatsapp_performance_menu" 
              name="Performance" 
              parent="whatsapp.whatsapp_menu_root" 
              sequence="50"/>
              
    <menuitem id="whatsapp_performance_dashboard_menu" 
              name="Message Performance" 
              parent="whatsapp_performance_menu" 
              action="whatsapp_performance_dashboard_action" 
              sequence="10"/>
              
    <menuitem id="whatsapp_account_performance_menu" 
              name="Account Performance" 
              parent="whatsapp_performance_menu" 
              action="whatsapp_account_performance_action" 
              sequence="20"/>
</odoo>
