# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'WhatsApp Messaging (Taqnyat.sa)',
    'category': 'Marketing/WhatsApp',
    'summary': 'Text your Contacts on WhatsApp using Taqnyat.sa API',
    'version': '2.0',
    'description': """This module integrates Odoo with WhatsApp using Taqnyat.sa WhatsApp Business API.

Features:
- Send automated WhatsApp notifications to any department within Odoo
- Send business documents (invoices, quotations, etc.) via WhatsApp
- Access and manage WhatsApp message templates directly from within Odoo
- Backward compatibility with existing Meta WhatsApp Business API configurations

Migration from Meta WhatsApp Business API to Taqnyat.sa API provides:
- Simplified authentication with Bearer token
- Better support for international phone numbers
- Reliable message delivery
- Cost-effective pricing
""",
    'depends': ['mail', 'phone_validation'],
    'data': [
        'data/ir_actions_server_data.xml',
        'data/ir_cron_data.xml',
        'data/ir_module_category_data.xml',
        # 'data/ir_config_parameter_data.xml',  # Temporarily disabled for testing
        'data/whatsapp_templates_preview.xml',
        # 'data/whatsapp_performance_cron.xml',  # Temporarily disabled for testing
        'security/res_groups.xml',
        'security/ir_rules.xml',
        'security/ir.model.access.csv',
        'wizard/whatsapp_preview_views.xml',
        'wizard/whatsapp_composer_views.xml',
        'views/discuss_channel_views.xml',
        'views/ir_actions_server_views.xml',
        'views/whatsapp_account_views.xml',
        'views/whatsapp_message_views.xml',
        'views/whatsapp_template_views.xml',
        'views/whatsapp_template_button_views.xml',
        'views/whatsapp_template_variable_views.xml',
        # 'views/whatsapp_performance_views.xml',  # Temporarily disabled for testing
        'views/res_config_settings_views.xml',
        'views/whatsapp_menus.xml',
        'views/res_partner_views.xml',
        'views/whatsapp_optin_views.xml',
    ],
    'demo': [
        'data/whatsapp_demo.xml',
    ],
    'external_dependencies': {
        'python': ['phonenumbers'],
    },
    'assets': {
        'web.assets_backend': [
            'whatsapp/static/src/scss/*.scss',
            'whatsapp/static/src/components/**/*.scss',
            'whatsapp/static/src/core/common/**/*',
            'whatsapp/static/src/core/web/**/*',
            'whatsapp/static/src/core/public_web/**/*',
            'whatsapp/static/src/**/common/**/*',
            'whatsapp/static/src/**/web/**/*',
            'whatsapp/static/src/components/**/*.js',
            'whatsapp/static/src/components/**/*.xml',
            'whatsapp/static/src/views/**/*',
            # Don't include dark mode files in light mode
            ('remove', 'whatsapp/static/src/**/*.dark.scss'),
        ],
        "web.assets_web_dark": [
            'whatsapp/static/src/**/*.dark.scss',
        ],
        'web.assets_unit_tests': [
            'whatsapp/static/tests/**/*',
        ],
    },
    'license': 'OEEL-1',
    'application': True,
    'installable': True,
    # 'post_init_hook': 'post_init_hook',  # Temporarily disabled to test basic loading
}
