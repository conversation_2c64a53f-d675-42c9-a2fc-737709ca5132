{"info": {"_postman_id": "6990311e-ea7f-411b-9c7b-6fa7a9165a19", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "## Introduction\n\n  \nTaqnyat is a licensed company from CITC in the Saudi Arabia kingdom, recently we have signed an agreement with a verified WhatsApp Business Solutions Provider specializing in WhatsApp Business API Access and hosting.\n\nAs a Provider for official business solution providers, we host the WhatsApp Business API. You can access and use the API by calling the endpoints provided, The WhatsApp Business API uses a REST API Architecture with JSON data formats. The API follows the standard HTTP request-response exchange.\n\nalso in Taqnyat, we have many other channels that can be provided for our customers, like SMS, Voice, Email, and Push notification, we just recently added the WhatsApp business API to the list with a variety of features WhatsApp already included for years WhatsApp is known as a preferred channel for users with more than 2.4 billion around the globe.\n\n### Why Taqnyat \n\nwhy go with taqnyat no directly with Whatsapp or any other business solution providers.\n\nfirst of all, we need to clarify that WhatsApp business API is a limited access service provided by Meta, Facebook company and going directly to WhatsApp is not recommended by WhatsApp them self, and they strongly advise businesses to work with one of the solution providers listed in a directory provided by Facebook.\n\nmost of the business solution providers are not available in the MENA, and if they are they don't have support for Arabic or are licensed from CITC, unlike us since taqnyat provide a flexible solution for the business and help them all the way to get started and keep them updated with 24/7/365 supporting, also taking care the tech, and business issues scale them and put 100% power to resolve them in no time.\n\n### About whatsApp API\n\nThe WhatsApp Business API client offers many of the features provided by the WhatsApp applications you already know from Android, iOS, and Web.\n\nThe difference is that this application can be deployed on a server, providing a local API that allows you to programmatically send and receive messages and integrate this workflow with your own systems (e.g., CRMs, contact center platforms, etc.).\n\nBe aware that, to use our API, businesses must complete a series of requirements, including, but not limited to:\n\n- Following Facebook [Display Name Guidelines](https://developers.facebook.com/docs/whatsapp/guides/display-name#display-name-guidelines).\n- Going through [Business Verification](https://developers.facebook.com/docs/development/release/business-verification).\n- Following [WhatsApp's legal policies](https://l.facebook.com/l.php?u=https://www.whatsapp.com/legal/&h=AT3ukEc05dQ2PlkffpeJGks2BnQUqfJon_qydglgsuhHTpl-w3uBH_2SJQIPP6Kv7t5_GJO1dP_r5QbJ5H62vGHsiXPt7HkkteBOqhqOHXD6ycA_YOo3jl3DDoTeUIjMSWOVdB-OKnX4g_c0nq4T5MC1wLk), including the [WhatsApp Commerce Policy](https://l.facebook.com/l.php?u=https://www.whatsapp.com/legal/commerce-policy/&h=AT3hqJ9NzHWDh6Uq7UOjv-KVOdxyroONNIlEaTmotXML0Kv6AlwF52eKVfupCN_DbQ7Dsw70j9VDgLVmlQVGW5MskNID-bV-zra6WkPvNdRWQNpQ35bmhIp7GzCCpiQFAmMN8wCAaH_57QKWVIIEVQIIJhQ) and the [WhatsApp Business Policy](https://l.facebook.com/l.php?u=https://www.whatsapp.com/legal/business-policy&h=AT0X25cYjNgvDE2BOED51v1a57DVkKKHYiVa3S1mYRPGh8QCGfhagkawayhB91sr1Nu9KNQcxMNQ2NJqdV6eqb0LYXwx0YBh7ZPUWBB1VAxFHTMR4xua_Qqhhf85BiyH8NDRX0lcTs5Z4lUdRLHuyoKm-k8).\n    \n\n## WBAPI Prerequisites\n\nBefore a business can access the WhatsApp Business API, each client has to go through an approval procedure, and list of requirments are needed.\n\n  \n\n### Before you go\n\n- We recommended the one who is going to use this documentation or APIs to have programming skills.\n- WhatsApp business API is paid service unlike whatsapp user app and whatsapp business app.\n- WhatsApp has two types of messages, notification message (template) , and customer care messages , each customer care messages last for 24h of the last message sent by end user\n- When you send whatsApp through API, mobile numbers that will receive the message must be in international format without 00 or symbol (+)\n- You must have an active account Registered with facebook before you go, you may contact [<EMAIL>](mailto:<EMAIL>) or account manager to get more details about the requirements and how to activate or your Registered your number.\n    \n\nyou must have all the [prerequirments](https://dev.taqnyat.sa/ar/doc/whatsapp/#prerequisites-list) needed to activate the service\n\n  \n\n### Pricing\n\nWhatsApp has switched from a notification-based pricing model to a conversation-based pricing model. Businesses are charged per conversation, which includes all messages delivered in a 24 hour session.\n\n**How It Works** WhatsApp Business API conversations fall into two categories that are priced differently:\n\n- **User-initiated**: A conversation that initiates in response to a user message. Whenever a business replies to a user within the 24 hour customer service window, that message will be associated with a user-initiated conversation. Businesses can send free-form messages within this 24 hour customer service window.\n- **Business-initiated**: A conversation that initiates from a business sending a user a message outside the 24 hour customer service window. Messages that initiate a business-initiated conversation will require a message template.\n    \n\nAll conversations are measured in fixed 24-hour sessions. A conversation starts when the first business message in a conversation is delivered, either initiated by the business or in reply to a user message. Businesses and users can exchange any number of messages, including template messages, within a 24 hour conversation session without incurring additional charges. Each 24 hour conversation session results in a single charge.\n\nCharges for conversations are based on the user’s country code. A user here is defined as the customer that your business is communicating with. Rates for business-initiated and user-initiated conversations vary by country or region.\n\nYou can download the individual rate cards and find the country to region mapping for countries where regional rates apply [whatsapp price list](https://developers.facebook.com/docs/whatsapp/pricing) .\n\n  \n\n### Prerequisites\n\n**To start using the WhatsApp Business API, you will need the following:**\n\n- A Facebook Business ID (with a verified Business if possible)\n- A valid phone number with the ability to receive phone calls or SMS\n- A callback server (typically provided by your Software Partner)\n    \n\n### Facebook Business ID \n\nYou need to have a Facebook Business ID before you can proceed to the WhatsApp approval process. To find your Business ID, log into the Facebook Business Manager of the business and navigate to Business Settings. The Business Manager ID is displayed under the business name.\n\nIf you don’t have a verified Facebook Business account, you need to create a Facebook Business Manager. To create a Business Manager:\n\n1. Go to business.facebook.com/overview.\n2. Click Create Account.\n3. Enter a name for your business, your name and work email address and click Next.\n4. Enter your business details and click Submit. More information about the creation of the Facebook Business Manager you can find [here](https://www.facebook.com/business/help/****************) .\n    \n\n### Message On Behalf request\n\nAfter the Facebook Business Manager ID is provided, you will receive a Message on Behalf request. You need to accept it, so Taqnyat can send messages on your behalf with your Facebook Business Manager ID.\n\nPlease navigate in your Facebook Business Manager Account to Requests > Received to accept the request.\n\n``` properties\nOnly the admin of the FBM account can see this request and accepted it ..\n\n\n```\n\n### WhatsApp approval procedure\n\nStart the process of verifying your business in Facebook Business Manager. This procedure helps to protect users from false information.\n\n  \n\n``` properties\nAttention: Once you’ve completed the official form with the legal business details, you won’t be able to change them.\n.\n\n```\n\nIt can take some time for Facebook to check all the details and approve your Business Verification.\n\n  \n\n### Phone number, Display Name & Review Process \n\n  \nAfter the verification is successful, you will be able to add your phone number and the Display Name for your WhatsApp business profile. During the WhatsApp approval procedure, the WhatsApp team reviews the client’s request according to the [WhatsApp Business Policy](https://www.whatsapp.com/legal/business-policy/) . You can check the status in the Facebook Business Manager account.\n\n### Official Business Account (Green Badge)\n\nIn contrast to the regular Business Account, an Official Business Account will receive a green checkmark\n\ngreen tick in the WhatsApp profile.\n\n### Requirements for approval\n\nTo receive the status, the company needs to reach a number of notability requirements.\n\n**This is why Official Business Account applications rarely get approved. Please apply for an OBA only if you're sure that your business is notorious and impactful enough for Facebook's requirements.**\n\n  \n\n### Understanding Notability\n\nNotability requires a business to represent a well-known, often searched brand or entity. This should not be taken as a signal of the authenticity of the business. A business is considered authentic if they have gone through the [Business Verification](https://www.facebook.com/business/help/****************) which verifies the business as a legal entity and their access to the business.\n\nNotability, on the other hand, reflects substantial presence in online news articles. Notability is assessed based on an account’s presence in news articles from publications with sizable audiences. Facebook does not consider paid or promotional content as sources for review, including business or app listings.\n\nOfficial business accounts are issued at the phone number and display name level. Facebook assesses notability for the Display Name of the business account that is requesting OBA status —If the display name is changed after receiving the OBA status, the account will need to go through the approval process again.\n\nAdditionally, previous OBA approvals within a WhatsApp Business Account do not guarantee approval for other numbers (with different display names) associated with that account. If your WABA contains one main parent brand and the phone number associated with that brand meet notability requirements, we suggest updating the display names for the child brands as follows: '{{sub-brand name}} by {{notable name}}'.\n\n### Denied Requests\n\nIf your OBA request has been denied, it means the Facebook team has carefully reviewed your account, and unfortunately, your account is not eligible for the OBA status at this time. Currently, these decisions cannot be appealed.\n\nBusinesses can continue to grow their presence and wait 90 days before submitting another Official Business Account request —It may take time to build the business's presence in news articles as described above.\n\nIn the meantime, this decision doesn't limit your ability to share your business details. Each phone number also has a business profile which includes profile picture, email, website, and business description. These are fields that you can edit at any time.\n\n## Let us Get started\n\nTo use taqnyat.sa API, you should have a taqnyat.sa account, here is an explanation of how you can register, checking your current balance, request a recharge for your balance through a “request recharge form” in taqnyat.sa website.\n\n1. Registration process: You can register on taqnyat.sa website through the following link: [Contact](https://www.taqnyat.sa/en/contact/) and go to Sales tab ,fill the form and hit send.\n2. Using bearer Tokens: bearer Tokens will have a unique value generated in the taqnyat.sa user account , because it provides a more secure connection with the API.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "WhatsApp Business API", "item": [{"name": "Template Messages", "item": [{"name": "Template | Text", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"{{Number}}\",\n    \"type\": \"template\",\n    \"template\": {\n        \"name\": \"{{template_name}}\",\n        \"language\": {\n            \"code\": \"en\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Template | Image", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "bearer", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"+xxxxxxxxxxxxxxxx\",\n    \"type\": \"template\",\n    \"template\": {\n        \"name\": \"{{templatename}}\",\n        \"language\": {\n            \"code\": \"ar\"\n        }\n    },\n    \"components\": [\n        {\n            \"type\": \"header\",\n            \"parameters\": [\n                {\n                    \"type\": \"image\",\n                    \"image\": {\n                        \"link\": \"http://commondatastorage.googleapis.com/codeskulptor-assets/lathrop/asteroid_brown.png\"\n                    }\n                }\n            ]\n        \n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Template | Interactive", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"+96xxxxxxxxxxx\",\n    \"type\": \"template\",\n    \"template\": {\n        \"name\": \"{{templalte-name}}\",\n        \"language\": {\n            \"code\": \"{{lang}}\"\n        }\n    },\n    \"components\": [\n        \n        {\n            \"type\": \"body\",\n            \"parameters\": [\n                {\n                    \"type\": \"text\",\n                    \"text\": \"{{1st-variable-value}}\"\n                },\n                {\n                    \"type\": \"text\",\n                    \"text\": \"{{2nd-variable-value}}\"\n                },\n                {\n                    \"type\": \"text\",\n                    \"text\": \"{{3rd-variable-value}}\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Template | Mix pdf& variables", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"to\": \"{{phone}}\",\r\n    \"type\": \"template\",\r\n    \"template\": {\r\n        \"name\": \"{{template_name}}\",\r\n        \"language\": {\r\n            \"code\": \"{{template_language_code}}\"\r\n        }\r\n    },\r\n    \"components\": [\r\n        {\r\n            \"type\": \"header\",\r\n            \"parameters\": [\r\n                {\r\n                     \"type\": \"document\",\r\n                    \"document\": {\r\n                        \"link\": \"{{pdf_URL}}\",\r\n                        \"filename\": \"{{document_name}}\"\r\n                    }\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"type\": \"body\",\r\n            \"parameters\": [\r\n                {\r\n                    \"type\": \"text\",\r\n                    \"text\": \"{{1s-Var}}\"\r\n                },\r\n                {\r\n                    \"type\": \"text\",\r\n                    \"text\": \"{{2nd_variable}}\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Template | PDF", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"to\": \"{{+Phone-number}}\",\r\n    \"type\": \"template\",\r\n    \"template\": {\r\n        \"name\": \"{{Temp-name}}\",\r\n        \"language\": {\r\n            \"code\": \"{{ar}}\"\r\n        }\r\n    },\r\n    \"components\": [\r\n        {\r\n            \"type\": \"header\",\r\n            \"parameters\": [\r\n                {\r\n                    \"type\": \"document\",\r\n                    \"document\": {\r\n                        \"link\": \"{{file-link}}\",\r\n                        \"filename\": \"File Name\"\r\n                    }\r\n                }\r\n            ]\r\n        \r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "OTP", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"to\": \"{{phone number}}\",\r\n    \"type\": \"template\",\r\n    \"template\": {\r\n        \"name\": \"{{template name}}\",\r\n        \"language\": {\r\n            \"code\": \"{{language code}}\"\r\n        },\r\n        \"components\": [\r\n            {\r\n                \"type\": \"body\",\r\n                \"parameters\": [\r\n                    {\r\n                        \"type\": \"text\",\r\n                        \"text\": \"{{otp_value}}\"\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"type\": \"button\",\r\n                \"sub_type\": \"url\",\r\n                \"index\": \"0\",\r\n                \"parameters\": [\r\n                    {\r\n                        \"type\": \"text\",\r\n                        \"text\": \"{{otp_value}}\"\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Failover <PERSON>py", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer xxxxxxx"}], "body": {"mode": "raw", "raw": "{\n    \"to\": \"{{phone number}}\",\n    \"type\": \"template\",\n    \"template\": {\n        \"name\": \"{{template name}}\",\n        \"language\": {\n            \"code\": \"{{language code}}\"\n        },\n        \"components\": [\n            {\n                \"type\": \"body\",\n                \"parameters\": [\n                    {\n                        \"type\": \"text\",\n                        \"text\": \"12345\"\n                    }\n                ]\n            },\n            {\n                \"type\": \"button\",\n                \"sub_type\": \"url\",\n                \"index\": \"0\",\n                \"parameters\": [\n                    {\n                        \"type\": \"text\",\n                        \"text\": \"12345\"\n                    }\n                ]\n            }\n        ]\n    },\n    \"sms\": {\n        \"sender\": \"taqnyat.sa\",\n        \"campaign\": \"campaignName3\",\n        \"body\": \"عربي\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/index.php", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", "index.php"]}, "description": "Generated from cURL: curl -X POST https://api.taqnyat.sa/wa/v2/messages/index.php \\\n-H \"Content-Type: application/json\" \\\n-H \"Authorization: Bearer xxxxxxx\" \\\n-d '{\n    \"to\": \"xxxxxxxx\",\n    \"type\": \"template\",\n    \"template\": {\n        \"language\": {\n            \"code\": \"ar\"\n        },\n        \"name\": \"leader_test\"\n    },\n    \"sms\": {\n        \"sender\": \"taqnyat.sa\",\n        \"campaign\": \"campaignName3\",\n        \"body\": \"عربي\"\n    }\n}'"}, "response": []}]}, {"name": "Conversation Messages", "item": [{"name": "Conversation | Text", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{Number}}\",\n  \"type\": \"text\",\n  \"text\": {\n    \"body\": \"your-text-message-content\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Conversation | Image", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"{{+966*******}}\",\n    \"type\": \"image\",\n    \"image\": {\n\t\t\"link\":\"https://www.be4e-marketing.com/wp-content/uploads/2019/12/Be-Digjhbsdfjhg.png\",\n\t\t\"caption\": \"Message-Caption\",\n\t\t\"filename\": \"hello\",\n\t\t\"provider\": {\n            \"name\" : \"your-mediaProvider-name\"\n         }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Conversation | Audio", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"{{Number}}\",\n    \"type\": \"audio\",\n    \"audio\": {\n\t\t\"link\":\"{{URL_Audio}}\",\n\t\t\"caption\": \"Message-Caption\",\n\t\t\"filename\": \"hello\",\n\t\t\"provider\": {\n            \"name\" : \"your-mediaProvider-name\"\n         }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "Conversation | Video", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"{{Number}}\",\n    \"type\": \"video\",\n    \"video\": {\n\t\t\"link\":\"{{URL_Audio}}\",\n\t\t\"caption\": \"Message-Caption\",\n\t\t\"filename\": \"hello\",\n\t\t\"provider\": {\n            \"name\" : \"your-mediaProvider-name\"\n         }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}, {"name": "list", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer 8d18d2f9045b7145ff81***********"}], "body": {"mode": "raw", "raw": "{\r\n   \"to\": \"966********\",\r\n   \"type\":\"interactive\",\r\n   \"interactive\":{\r\n    \"type\": \"list\",\r\n    \"header\": {\r\n      \"type\": \"text\",\r\n      \"text\": \"Delivery Address\"\r\n    },\r\n    \"body\": {\r\n      \"text\": \"Thanks for your order! Select your delivery address.\"\r\n    },\r\n    \"footer\": {\r\n      \"text\": \"Please choose an option\"\r\n    },\r\n    \"action\": {\r\n      \"button\": \"Select\",\r\n      \"sections\": [\r\n        {\r\n          \"title\": \"Address Options\",\r\n          \"rows\": [\r\n            {\r\n              \"id\": \"address_1\",\r\n              \"title\": \"Home\",\r\n              \"description\": \"123 Main St\"\r\n            },\r\n            {\r\n              \"id\": \"address_2\",\r\n              \"title\": \"Office\",\r\n              \"description\": \"456 Office Park\"\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}, "description": "Generated from cURL: curl --location 'https://api.taqnyat.sa/wa/v2/messages/' \\\r\n--header 'Content-Type: application/json' \\\r\n--header 'Authorization: Bearer 8d18d2f9045b7145ff81***********' \\\r\n--data '{\r\n   \"to\": \"966********\",\r\n   \"type\":\"interactive\",\r\n   \"interactive\":{\r\n    \"type\": \"list\",\r\n    \"header\": {\r\n      \"type\": \"text\",\r\n      \"text\": \"Delivery Address\"\r\n    },\r\n    \"body\": {\r\n      \"text\": \"Thanks for your order! Select your delivery address.\"\r\n    },\r\n    \"footer\": {\r\n      \"text\": \"Please choose an option\"\r\n    },\r\n    \"action\": {\r\n      \"button\": \"Select\",\r\n      \"sections\": [\r\n        {\r\n          \"title\": \"Address Options\",\r\n          \"rows\": [\r\n            {\r\n              \"id\": \"address_1\",\r\n              \"title\": \"Home\",\r\n              \"description\": \"123 Main St\"\r\n            },\r\n            {\r\n              \"id\": \"address_2\",\r\n              \"title\": \"Office\",\r\n              \"description\": \"456 Office Park\"\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}'"}, "response": []}, {"name": "Conversation | Document", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"to\": \"{{Number}}\",\n    \"type\": \"document\",\n    \"document\": {\n\t\t\"link\":\"{{URL_Document}}\",\n\t\t\"caption\": \"Message-Caption\",\n\t\t\"filename\": \"hello\",\n\t\t\"provider\": {\n            \"name\" : \"your-mediaProvider-name\"\n         }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/messages/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "messages", ""]}}, "response": []}]}, {"name": "Opt-in", "item": [{"name": "Opt-In sync", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"blocking\": \"wait\",\n    \"contacts\": [\n        \"{{Number}}\"\n    ],\n    \"force_check\": true \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/contacts/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "contacts", ""]}}, "response": []}, {"name": "Opt-In async", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON>}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"blocking\": \"no_wait\",\n    \"contacts\": [\n        \"{{Number}}\"\n    ],\n    \"force_check\": true \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/contacts/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "contacts", ""]}}, "response": []}]}, {"name": "add templates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "5d66945100a5b506f59a7cddbd8a54e3"}], "body": {"mode": "raw", "raw": "{\r\n   \"name\":\"example_name\",\r\n   \"language\":\"ar\",\r\n   \"allow_category_change\":true,\r\n   \"category\":\"UTILITY\",\r\n   \"components\":[\r\n      {\r\n         \"type\":\"BODY\",\r\n         \"text\":\"Lorem ipsum dolor sit amet\",\r\n         \"example\":{\r\n            \"body_text\":[\r\n               [\r\n                  \"\"\r\n               ]\r\n            ]\r\n         }\r\n      },\r\n      {\r\n         \"type\":\"HEADER\",\r\n         \"format\":\"TEXT\",\r\n         \"text\":\"Lorem ipsum\"\r\n      },\r\n      {\r\n         \"type\":\"FOOTER\",\r\n         \"text\":\"Lorem ipsum\"\r\n      },\r\n      {\r\n         \"type\":\"BUTTONS\",\r\n         \"buttons\":[\r\n            {\r\n               \"type\":\"URL\",\r\n               \"text\":\"your-url-button-text\",\r\n               \"url\":\"https://website-example.com/\"\r\n            },\r\n            {\r\n               \"type\":\"PHONE_NUMBER\",\r\n               \"text\":\"Lorem ipsum\",\r\n               \"phone_number\":\"962797027787\"\r\n            }\r\n         ]\r\n      }\r\n   ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.taqnyat.sa/wa/v2/templates/", "protocol": "https", "host": ["api", "taqnyat", "sa"], "path": ["wa", "v2", "templates", ""]}, "description": "Generated from cURL: curl --location 'https://api.taqnyat.sa/wa/v2/templates/' \\\r\n--header 'Content-Type: application/json' \\\r\n--header 'Authorization: 5d66945100a5b506f59a7cddbd8a54e3' \\\r\n--data '{\r\n   \"name\":\"example_name\",\r\n   \"language\":\"ar\",\r\n   \"allow_category_change\":true,\r\n   \"category\":\"UTILITY\",\r\n   \"components\":[\r\n      {\r\n         \"type\":\"BODY\",\r\n         \"text\":\"Lorem ipsum dolor sit amet\",\r\n         \"example\":{\r\n            \"body_text\":[\r\n               [\r\n                  \"\"\r\n               ]\r\n            ]\r\n         }\r\n      },\r\n      {\r\n         \"type\":\"HEADER\",\r\n         \"format\":\"TEXT\",\r\n         \"text\":\"Lorem ipsum\"\r\n      },\r\n      {\r\n         \"type\":\"FOOTER\",\r\n         \"text\":\"Lorem ipsum\"\r\n      },\r\n      {\r\n         \"type\":\"BUTTONS\",\r\n         \"buttons\":[\r\n            {\r\n               \"type\":\"URL\",\r\n               \"text\":\"your-url-button-text\",\r\n               \"url\":\"https://website-example.com/\"\r\n            },\r\n            {\r\n               \"type\":\"PHONE_NUMBER\",\r\n               \"text\":\"Lorem ipsum\",\r\n               \"phone_number\":\"962797027787\"\r\n            }\r\n         ]\r\n      }\r\n   ]\r\n}'"}, "response": []}]}], "variable": [{"key": "bearer", "value": "Bearer"}]}