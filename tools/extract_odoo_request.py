#!/usr/bin/env python3
"""
Script to extract and analyze the exact request being sent by Odoo
This helps compare Odoo's request with working Postman format
"""

import json
import re
import sys
from datetime import datetime

def parse_odoo_logs(log_content):
    """Parse Odoo logs to extract template submission requests"""
    
    print("🔍 ANALYZING ODOO LOGS")
    print("=" * 50)
    
    # Look for template submission log entries
    submission_patterns = [
        r'Template submission payload: (.+)',
        r'Submit new template for account .+ payload: (.+)',
        r'API request data: (.+)'
    ]
    
    found_requests = []
    
    for pattern in submission_patterns:
        matches = re.findall(pattern, log_content, re.MULTILINE)
        for match in matches:
            try:
                # Try to parse as JSON
                request_data = json.loads(match)
                found_requests.append(request_data)
                print(f"✅ Found request: {json.dumps(request_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"⚠️  Found non-JSON request: {match}")
    
    # Look for response information
    response_patterns = [
        r'Template submission response status: (\d+)',
        r'Template submission response body: (.+)',
        r'API request failed with status (\d+)'
    ]
    
    print("\n📥 RESPONSE INFORMATION:")
    for pattern in response_patterns:
        matches = re.findall(pattern, log_content, re.MULTILINE)
        for match in matches:
            print(f"Response info: {match}")
    
    return found_requests

def compare_with_postman(odoo_request):
    """Compare Odoo request with Postman format"""
    
    print("\n🔄 COMPARING WITH POSTMAN FORMAT")
    print("=" * 50)
    
    # Expected Postman format
    postman_format = {
        "name": "example_name",
        "language": "ar",
        "allow_category_change": True,
        "category": "UTILITY",
        "components": [
            {
                "type": "BODY",
                "text": "Lorem ipsum dolor sit amet",
                "example": {
                    "body_text": [[""]]
                }
            }
        ]
    }
    
    print("📤 Postman Format:")
    print(json.dumps(postman_format, indent=2))
    
    print("\n📤 Odoo Format:")
    print(json.dumps(odoo_request, indent=2))
    
    # Compare fields
    differences = []
    
    # Check top-level fields
    for key in postman_format.keys():
        if key not in odoo_request:
            differences.append(f"Missing field: {key}")
        elif odoo_request[key] != postman_format[key] and key != "name" and key != "components":
            differences.append(f"Different value for {key}: Odoo={odoo_request[key]}, Postman={postman_format[key]}")
    
    # Check components structure
    if "components" in odoo_request and odoo_request["components"]:
        odoo_body = odoo_request["components"][0]
        postman_body = postman_format["components"][0]
        
        if "example" in odoo_body and "example" in postman_body:
            odoo_example = odoo_body["example"]
            postman_example = postman_body["example"]
            
            if odoo_example != postman_example:
                differences.append(f"Example format differs: Odoo={odoo_example}, Postman={postman_example}")
    
    if differences:
        print("\n❌ DIFFERENCES FOUND:")
        for diff in differences:
            print(f"  • {diff}")
    else:
        print("\n✅ Formats match!")
    
    return differences

def generate_fix_recommendations(differences):
    """Generate specific fix recommendations"""
    
    print("\n🛠️  FIX RECOMMENDATIONS")
    print("=" * 50)
    
    if not differences:
        print("✅ No differences found - the issue might be authentication")
        print("Recommendations:")
        print("• Check if your token has template creation permissions")
        print("• Verify account has sufficient balance")
        print("• Contact Taqnyat.sa support")
        return
    
    for diff in differences:
        if "example" in diff.lower():
            print("🔧 Fix example field format:")
            print("  In whatsapp/models/whatsapp_template.py, line ~395:")
            print("  Change: body_component['example'] = {'body_text': demo_values}")
            print("  To:     body_component['example'] = {'body_text': [demo_values]}")
        
        elif "language" in diff.lower():
            print("🔧 Fix language code:")
            print("  Ensure language is 'ar' not 'ar_SA'")
        
        elif "category" in diff.lower():
            print("🔧 Fix category:")
            print("  Ensure category is uppercase (UTILITY, MARKETING, etc.)")

def main():
    """Main function"""
    
    print("🔍 ODOO REQUEST ANALYZER")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    
    if len(sys.argv) > 1:
        log_file = sys.argv[1]
        try:
            with open(log_file, 'r') as f:
                log_content = f.read()
        except FileNotFoundError:
            print(f"❌ Log file not found: {log_file}")
            return
    else:
        print("📋 Paste your Odoo log content below (Ctrl+D when done):")
        log_content = sys.stdin.read()
    
    if not log_content.strip():
        print("❌ No log content provided")
        return
    
    # Parse logs
    requests = parse_odoo_logs(log_content)
    
    if not requests:
        print("❌ No template submission requests found in logs")
        print("\nTips:")
        print("• Make sure logging level is set to INFO")
        print("• Look for lines containing 'Template submission payload'")
        print("• Try submitting a template and check logs immediately")
        return
    
    # Analyze the first request found
    odoo_request = requests[0]
    differences = compare_with_postman(odoo_request)
    generate_fix_recommendations(differences)
    
    print(f"\n📊 Analysis complete. Found {len(requests)} request(s)")

if __name__ == "__main__":
    main()
