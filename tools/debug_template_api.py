#!/usr/bin/env python3
"""
Debug script to compare Postman vs Odoo template submission requests
This script helps identify differences between successful Postman requests and failing Odoo requests
"""

import json
import requests
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
_logger = logging.getLogger(__name__)

class TaqnyatTemplateDebugger:
    def __init__(self, bearer_token):
        self.token = bearer_token
        self.base_url = "https://api.taqnyat.sa/wa/v2"
        
    def test_postman_format(self):
        """Test the exact format from your Postman collection"""
        print("\n" + "="*60)
        print("🔍 TESTING POSTMAN FORMAT")
        print("="*60)
        
        # This is the exact format from your Postman screenshot
        postman_payload = {
            "name": "example_name2",
            "language": "ar",
            "allow_category_change": True,
            "category": "UTILITY",
            "components": [
                {
                    "type": "BODY",
                    "text": "Lorem ipsum dolor sit amet",
                    "example": {
                        "body_text": [
                            ""
                        ]
                    }
                }
            ]
        }
        
        return self._make_request(postman_payload, "Postman Format")
    
    def test_odoo_format(self):
        """Test the current Odoo format"""
        print("\n" + "="*60)
        print("🔍 TESTING ODOO FORMAT")
        print("="*60)
        
        # This is how Odoo currently formats the request
        odoo_payload = {
            'name': 'example_name2',
            'language': 'ar',
            'allow_category_change': True,
            'category': 'UTILITY',
            'components': [
                {
                    'type': 'BODY',
                    'text': 'Lorem ipsum dolor sit amet',
                    'example': {
                        'body_text': [['Sample Value']]
                    }
                }
            ]
        }
        
        return self._make_request(odoo_payload, "Odoo Format")
    
    def test_minimal_format(self):
        """Test minimal format without examples"""
        print("\n" + "="*60)
        print("🔍 TESTING MINIMAL FORMAT")
        print("="*60)
        
        minimal_payload = {
            "name": "example_name3",
            "language": "ar", 
            "allow_category_change": True,
            "category": "UTILITY",
            "components": [
                {
                    "type": "BODY",
                    "text": "Lorem ipsum dolor sit amet"
                }
            ]
        }
        
        return self._make_request(minimal_payload, "Minimal Format")
    
    def test_different_auth_formats(self):
        """Test different authentication header formats"""
        print("\n" + "="*60)
        print("🔍 TESTING AUTHENTICATION FORMATS")
        print("="*60)
        
        payload = {
            "name": "auth_test",
            "language": "ar",
            "category": "UTILITY", 
            "components": [{"type": "BODY", "text": "Test message"}]
        }
        
        # Test 1: Bearer prefix (current Odoo)
        print("\n📋 Test 1: Bearer prefix format")
        headers1 = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        self._make_raw_request(payload, headers1, "Bearer Prefix")
        
        # Test 2: Direct token (Postman format)
        print("\n📋 Test 2: Direct token format")
        headers2 = {
            'Authorization': self.token,
            'Content-Type': 'application/json'
        }
        self._make_raw_request(payload, headers2, "Direct Token")
    
    def _make_request(self, payload, test_name):
        """Make a request using current Odoo authentication method"""
        headers = {
            'Authorization': self.token,  # Direct token like Postman
            'Content-Type': 'application/json'
        }
        return self._make_raw_request(payload, headers, test_name)
    
    def _make_raw_request(self, payload, headers, test_name):
        """Make raw HTTP request and analyze response"""
        url = f"{self.base_url}/templates/"
        
        print(f"\n📤 {test_name} Request:")
        print(f"URL: {url}")
        print(f"Headers: {json.dumps(headers, indent=2)}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )
            
            print(f"\n📥 {test_name} Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")
            
            # Try to parse JSON response
            try:
                response_json = response.json()
                print(f"JSON Response: {json.dumps(response_json, indent=2)}")
                
                if response.status_code == 201 and response_json.get('id'):
                    print(f"✅ {test_name}: SUCCESS - Template created with ID: {response_json['id']}")
                    return True
                else:
                    print(f"❌ {test_name}: FAILED")
                    return False
                    
            except json.JSONDecodeError:
                print(f"Raw Response: {response.text}")
                print(f"❌ {test_name}: FAILED - Invalid JSON response")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {test_name}: NETWORK ERROR - {str(e)}")
            return False
    
    def run_comprehensive_test(self):
        """Run all tests and provide analysis"""
        print("🚀 TAQNYAT.SA TEMPLATE API DEBUGGING")
        print("="*60)
        print(f"Timestamp: {datetime.now()}")
        print(f"Token: {self.token[:10]}...")
        print(f"Base URL: {self.base_url}")
        
        results = {}
        
        # Test different authentication formats first
        self.test_different_auth_formats()
        
        # Test different payload formats
        results['postman'] = self.test_postman_format()
        results['odoo'] = self.test_odoo_format() 
        results['minimal'] = self.test_minimal_format()
        
        # Analysis
        print("\n" + "="*60)
        print("📊 ANALYSIS SUMMARY")
        print("="*60)
        
        successful_formats = [fmt for fmt, success in results.items() if success]
        failed_formats = [fmt for fmt, success in results.items() if not success]
        
        if successful_formats:
            print(f"✅ Successful formats: {', '.join(successful_formats)}")
        if failed_formats:
            print(f"❌ Failed formats: {', '.join(failed_formats)}")
        
        print("\n🔧 RECOMMENDATIONS:")
        if results.get('postman') and not results.get('odoo'):
            print("• Odoo format differs from working Postman format")
            print("• Check authentication header format")
            print("• Check payload structure differences")
        elif results.get('minimal') and not results.get('odoo'):
            print("• Remove example field from Odoo payload")
            print("• Simplify component structure")
        elif not any(results.values()):
            print("• All formats failed - check token validity")
            print("• Verify API endpoint accessibility")
            print("• Contact Taqnyat.sa support")
        
        return results

def main():
    """Main function to run the debugger"""
    print("🔧 Taqnyat.sa Template API Debugger")
    print("This script helps debug template submission issues")
    
    # Get token from user
    token = input("\nEnter your Bearer token: ").strip()
    if not token:
        print("❌ Token is required")
        return
    
    debugger = TaqnyatTemplateDebugger(token)
    results = debugger.run_comprehensive_test()
    
    print(f"\n📋 Test completed. Results: {results}")

if __name__ == "__main__":
    main()
