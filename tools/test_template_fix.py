#!/usr/bin/env python3
"""
Test script to verify the template submission fixes
This can be run from Odoo shell to test the improvements
"""

def test_template_submission_fix():
    """
    Test the template submission fix by creating a simple template
    Run this from Odoo shell:
    
    from odoo.addons.whatsapp.tools.test_template_fix import test_template_submission_fix
    test_template_submission_fix()
    """
    try:
        from odoo import registry, SUPERUSER_ID
        from odoo.api import Environment
        
        # Get Odoo environment
        env = Environment(registry(), SUPERUSER_ID, {})
        
        print("🔧 Testing WhatsApp Template Submission Fix")
        print("=" * 50)
        
        # Find WhatsApp account
        wa_account = env['whatsapp.account'].search([], limit=1)
        if not wa_account:
            print("❌ No WhatsApp account found. Please create one first.")
            return False
        
        print(f"📱 Using account: {wa_account.name}")
        print(f"🔑 Token length: {len(wa_account.token) if wa_account.token else 0} characters")
        
        if not wa_account.token:
            print("❌ No Bearer token configured. Please add your token first.")
            return False
        
        # Test 1: Run diagnostic
        print("\n🔍 Running diagnostic tests...")
        try:
            results = wa_account._run_diagnostic_tests()
            formatted_results = wa_account._format_diagnostic_results(results)
            print(formatted_results)
            
            if not results['success']:
                print("\n❌ Diagnostic tests failed. Please resolve the issues above before testing template submission.")
                return False
                
        except Exception as e:
            print(f"❌ Diagnostic test failed: {str(e)}")
            return False
        
        # Test 2: Create a simple test template
        print("\n📝 Creating test template...")
        try:
            test_template = env['whatsapp.template'].create({
                'name': 'Test Template Fix',
                'template_name': 'test_template_fix',
                'wa_account_id': wa_account.id,
                'model': 'res.partner',
                'template_type': 'utility',
                'lang_code': 'en',
                'body': 'Hello, this is a test message to verify the template submission fix.',
                'allow_category_change': True,
            })
            print(f"✅ Test template created: {test_template.name}")
            
        except Exception as e:
            print(f"❌ Failed to create test template: {str(e)}")
            return False
        
        # Test 3: Submit the template
        print("\n📤 Submitting template for approval...")
        try:
            test_template.button_submit_template()
            print(f"✅ Template submitted successfully!")
            print(f"📊 Template status: {test_template.status}")
            print(f"🆔 Template ID: {test_template.wa_template_uid}")
            
            # Clean up - delete the test template
            if test_template.wa_template_uid:
                print("\n🧹 Cleaning up test template...")
                try:
                    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
                    wa_api = WhatsAppApi(wa_account)
                    wa_api._delete_template(test_template.wa_template_uid)
                    print("✅ Test template cleaned up from Taqnyat.sa")
                except:
                    print("⚠️ Could not clean up test template from Taqnyat.sa (this is normal)")
            
            # Delete from Odoo
            test_template.unlink()
            print("✅ Test template removed from Odoo")
            
            return True
            
        except Exception as e:
            print(f"❌ Template submission failed: {str(e)}")
            
            # Clean up on failure
            try:
                test_template.unlink()
                print("🧹 Test template cleaned up from Odoo")
            except:
                pass
            
            return False
    
    except Exception as e:
        print(f"❌ Test failed with unexpected error: {str(e)}")
        return False

def test_template_body_component():
    """Test the template body component generation"""
    try:
        from odoo import registry, SUPERUSER_ID
        from odoo.api import Environment
        
        env = Environment(registry(), SUPERUSER_ID, {})
        
        print("\n🧪 Testing Template Body Component Generation")
        print("=" * 50)
        
        # Find WhatsApp account
        wa_account = env['whatsapp.account'].search([], limit=1)
        if not wa_account:
            print("❌ No WhatsApp account found")
            return False
        
        # Test 1: Template without variables
        print("\n📝 Test 1: Template without variables")
        template_no_vars = env['whatsapp.template'].new({
            'wa_account_id': wa_account.id,
            'body': 'Hello, this is a simple message without variables.',
        })
        
        body_component = template_no_vars._get_template_body_component()
        print(f"Body component: {body_component}")
        
        # Should not have example field
        if 'example' not in body_component:
            print("✅ Correctly omitted example field for template without variables")
        else:
            print("⚠️ Example field present for template without variables")
        
        # Test 2: Template with variables
        print("\n📝 Test 2: Template with variables")
        template_with_vars = env['whatsapp.template'].new({
            'wa_account_id': wa_account.id,
            'body': 'Hello {{1}}, your order {{2}} is ready.',
        })
        
        # Create variables
        var1 = env['whatsapp.template.variable'].new({
            'name': '{{1}}',
            'demo_value': 'John',
            'line_type': 'body',
        })
        var2 = env['whatsapp.template.variable'].new({
            'name': '{{2}}',
            'demo_value': 'ORD001',
            'line_type': 'body',
        })
        template_with_vars.variable_ids = [var1, var2]
        
        body_component = template_with_vars._get_template_body_component()
        print(f"Body component: {body_component}")
        
        # Should have example field with demo values
        if 'example' in body_component:
            example = body_component['example']
            print(f"Example field: {example}")
            if 'body_text' in example and len(example['body_text']) > 0:
                print("✅ Correctly included example field with demo values")
            else:
                print("⚠️ Example field format incorrect")
        else:
            print("❌ Missing example field for template with variables")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script should be run from Odoo shell:")
    print("from odoo.addons.whatsapp.tools.test_template_fix import test_template_submission_fix")
    print("test_template_submission_fix()")
