#!/usr/bin/env python3
"""
Simple test to verify module imports correctly
"""

def test_module_imports():
    """Test that all module components import correctly"""
    print("🔧 Testing WhatsApp Module Imports")
    print("=" * 40)
    
    try:
        # Test basic imports
        print("📦 Testing basic imports...")
        import logging
        import re
        from markupsafe import Markup
        print("✅ Basic imports successful")
        
        # Test model imports (without Odoo environment)
        print("📝 Testing model structure...")
        
        # Test if our new model file is syntactically correct
        with open('whatsapp/models/whatsapp_direct_message.py', 'r') as f:
            content = f.read()
            # Basic syntax check
            compile(content, 'whatsapp_direct_message.py', 'exec')
        print("✅ WhatsApp Direct Message model syntax OK")
        
        # Test API enhancement
        with open('whatsapp/tools/whatsapp_api.py', 'r') as f:
            content = f.read()
            compile(content, 'whatsapp_api.py', 'exec')
        print("✅ Enhanced WhatsApp API syntax OK")
        
        # Test JavaScript files
        print("🌐 Testing JavaScript files...")
        import os
        js_files = [
            'whatsapp/static/src/discuss/core/common/composer_patch.js',
            'whatsapp/static/src/core/common/store_service_patch.js'
        ]
        
        for js_file in js_files:
            if os.path.exists(js_file):
                print(f"✅ {js_file} exists")
            else:
                print(f"❌ {js_file} missing")
        
        # Test XML files
        print("📄 Testing XML files...")
        xml_files = [
            'whatsapp/views/whatsapp_direct_message_views.xml',
            'whatsapp/static/src/discuss/core/common/composer_patch.xml'
        ]
        
        for xml_file in xml_files:
            if os.path.exists(xml_file):
                print(f"✅ {xml_file} exists")
            else:
                print(f"❌ {xml_file} missing")
        
        # Test manifest
        print("📋 Testing manifest...")
        with open('whatsapp/__manifest__.py', 'r') as f:
            content = f.read()
            compile(content, '__manifest__.py', 'exec')
        print("✅ Manifest syntax OK")
        
        print("\n🎉 All module components import successfully!")
        print("\n📋 Summary:")
        print("✅ Python models syntax correct")
        print("✅ JavaScript files present")
        print("✅ XML views present")
        print("✅ Manifest file correct")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

if __name__ == "__main__":
    test_module_imports()
