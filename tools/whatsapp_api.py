
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import json
import logging
import requests
import threading

from odoo import _
from odoo.exceptions import RedirectWarning
from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError
from odoo.addons.whatsapp.tools.taqnyat_api_service import (
    TaqnyatApiService, TaqnyatMessageService, TaqnyatMediaService, TaqnyatTemplateService
)
from odoo.addons.whatsapp.tools.session_manager import SessionWindowManager
from odoo.addons.whatsapp.tools.taqnyat_config import TaqnyatConfig

_logger = logging.getLogger(__name__)

DEFAULT_ENDPOINT = "https://api.taqnyat.sa/wa/v2"

class WhatsAppApi:
    def __init__(self, wa_account_id):
        wa_account_id.ensure_one()
        self.wa_account_id = wa_account_id
        # For Taqnyat.sa API, we only need the bearer token
        self.token = wa_account_id.sudo().token
        self.is_shared_account = False

        # Initialize enhanced service classes
        self.config = TaqnyatConfig()
        self.api_service = TaqnyatApiService(wa_account_id)
        self.message_service = TaqnyatMessageService(wa_account_id)
        self.media_service = TaqnyatMediaService(wa_account_id)
        self.template_service = TaqnyatTemplateService(wa_account_id)
        self.session_manager = SessionWindowManager(wa_account_id.env)

    def _get_current_time(self):
        """Get current time for performance tracking"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def _safe_json_parse(self, response):
        """Safely parse JSON response with proper error handling"""
        try:
            return response.json()
        except (ValueError, json.JSONDecodeError):
            error_details = f"Status: {response.status_code}, URL: {response.url}, Content: {response.text[:500]}"
            _logger.error("Invalid JSON response from Taqnyat.sa API. %s", error_details)

            # Since __api_requests now handles status codes and HTML responses,
            # this should only be called for valid HTTP responses that aren't valid JSON
            raise WhatsAppError(_("Invalid JSON response from Taqnyat.sa API. Response: %s") % response.text[:200], 'account')

    def __api_requests(self, request_type, url, auth_type="bearer", params=False, headers=None, data=False, files=False, endpoint_include=False):
        if getattr(threading.current_thread(), 'testing', False):
            raise WhatsAppError("API requests disabled in testing.")

        headers = headers or {}
        params = params or {}
        if not self.token:
            action = self.wa_account_id.env.ref('whatsapp.whatsapp_account_action')
            raise RedirectWarning(_("To use WhatsApp Configure it first"), action=action.id, button_text=_("Configure Whatsapp Business Account"))

        # Taqnyat.sa authentication - handle both token formats
        # Check if token already contains "Bearer " prefix
        if self.token.startswith('Bearer '):
            headers.update({'Authorization': self.token})
        else:
            headers.update({'Authorization': f"Bearer {self.token}"})

        call_url = (DEFAULT_ENDPOINT + url) if not endpoint_include else url

        # Enhanced logging for debugging
        _logger.info("API Request: %s %s", request_type, call_url)
        _logger.info("Headers: %s", {k: v[:10] + '...' if k == 'Authorization' and len(str(v)) > 10 else v for k, v in headers.items()})
        if data:
            _logger.info("Request payload: %s", data[:500] if isinstance(data, str) else str(data)[:500])

        try:
            res = requests.request(request_type, call_url, params=params, headers=headers, data=data, files=files, timeout=30)
        except requests.exceptions.Timeout:
            raise WhatsAppError(_("Request timeout. The Taqnyat.sa API is taking too long to respond. Please try again later."), 'network')
        except requests.exceptions.ConnectionError:
            raise WhatsAppError(_("Connection error. Unable to reach Taqnyat.sa API. Please check your internet connection."), 'network')
        except requests.exceptions.RequestException as e:
            _logger.error("Network error during API request: %s", str(e))
            raise WhatsAppError(_("Network error: %s") % str(e), 'network')

        # Enhanced logging for response analysis
        _logger.info("API Response: Status %s, Content-Type: %s", res.status_code, res.headers.get('Content-Type', 'Unknown'))
        if res.status_code != 200:
            _logger.info("Response body: %s", res.text[:500])

        # First check HTTP status codes for authentication/authorization errors
        if res.status_code == 401:
            # Try to parse JSON error for more specific message
            try:
                error_json = self._safe_json_parse(res)
                if error_json and error_json.get('reason') == 'No such bot/bearer combination':
                    raise WhatsAppError(_(
                        "Authentication failed: Invalid Bearer token.\n\n"
                        "🔑 TROUBLESHOOTING:\n"
                        "• Verify your Bearer token is correct\n"
                        "• Check if your token has expired\n"
                        "• Ensure your account has WhatsApp API access enabled\n"
                        "• Contact Taqnyat.sa support if the issue persists\n\n"
                        "Error: %s"
                    ) % error_json.get('message', 'No such bot/bearer combination'), 'account')
                else:
                    raise WhatsAppError(_("Authentication failed. Invalid Bearer token. Please verify your token is correct and active."), 'account')
            except (json.JSONDecodeError, ValueError):
                raise WhatsAppError(_("Authentication failed. Invalid Bearer token. Please verify your token is correct and active."), 'account')
        elif res.status_code == 403:
            raise WhatsAppError(_("Access forbidden. Your Bearer token does not have permission to access WhatsApp API. Please contact Taqnyat.sa support to activate API access."), 'account')
        elif res.status_code == 404:
            raise WhatsAppError(_("API endpoint not found. Please verify the Taqnyat.sa API configuration."), 'account')
        elif res.status_code >= 500:
            raise WhatsAppError(_("Taqnyat.sa server error (Status: %s). Please try again later.") % res.status_code, 'account')
        elif not res.ok:
            # For other non-2xx status codes, try to get detailed error information
            try:
                error_json = self._safe_json_parse(res)
                if error_json and isinstance(error_json, dict):
                    # Extract detailed error information
                    error_msg = error_json.get('message', '')
                    error_details = error_json.get('details', '')
                    error_code = error_json.get('code', '')

                    detailed_error = f"API request failed with status {res.status_code}"
                    if error_msg:
                        detailed_error += f"\nError: {error_msg}"
                    if error_details:
                        detailed_error += f"\nDetails: {error_details}"
                    if error_code:
                        detailed_error += f"\nCode: {error_code}"

                    # Add response text if no structured error found
                    if not (error_msg or error_details):
                        detailed_error += f"\nResponse: {res.text[:200]}"

                    raise WhatsAppError(_(detailed_error), 'account')
                else:
                    raise WhatsAppError(_("API request failed with status %s. Response: %s") % (res.status_code, res.text[:200]), 'account')
            except (json.JSONDecodeError, ValueError):
                # If JSON parsing fails, show raw response
                raise WhatsAppError(_("API request failed with status %s. Response: %s") % (res.status_code, res.text[:200]), 'account')

        # Check if response is HTML instead of JSON (common when API access is not activated)
        content_type = res.headers.get('Content-Type', '').lower()
        is_html = 'text/html' in content_type or res.text.strip().startswith('<')

        if is_html:
            raise WhatsAppError(_(
                "API returned HTML instead of JSON. This usually means:\n\n"
                "🔑 AUTHENTICATION ISSUE:\n"
                "• Your Bearer token is not activated for WhatsApp Business API\n"
                "• Your account needs API access activation\n\n"
                "📞 NEXT STEPS:\n"
                "• Contact Taqnyat.sa support to enable WhatsApp API access\n"
                "• Verify your token has WhatsApp Business API permissions\n"
                "• Check if your account subscription includes API access\n\n"
                "Response preview: %s"
            ) % res.text[:200], 'account')

        # Now try to parse JSON and check for API-level errors
        try:
            response_json = self._safe_json_parse(res)
            if 'error' in response_json:
                raise WhatsAppError(*self._prepare_error_response(response_json))
        except WhatsAppError:
            raise
        except Exception:
            # If JSON parsing fails but status is OK, it might be an unexpected response format
            raise WhatsAppError(_("Unexpected response format from Taqnyat.sa API. Expected JSON but got: %s") % content_type, 'account')

        return res

    def _prepare_error_response(self, response):
        """
            This method is used to prepare error response
            :return tuple[str, int]: (error_message, whatsapp_error_code | -1)
        """
        if response.get('error'):
            error = response['error']
            desc = error.get('message', '')
            desc += (' - ' + error['error_user_title']) if error.get('error_user_title') else ''
            desc += ('\n\n' + error['error_user_msg']) if error.get('error_user_msg') else ''
            code = error.get('code', 'odoo')
            return (desc if desc else _("Non-descript Error"), code)
        return (_("Something went wrong when contacting WhatsApp, please try again later. If this happens frequently, contact support."), -1)

    def _get_all_template(self, fetch_all=False):
        """
            This method is used to get all the template from the WhatsApp Business Account
            Enhanced with performance tracking and caching.

            API Documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        """
        if self.is_shared_account:
            raise WhatsAppError(failure_type='account')

        template_url = "/templates/"
        start_time = self._get_current_time()
        _logger.info("Sync templates for account %s [%s] started at %s",
                    self.wa_account_id.name, self.wa_account_id.id, start_time)
        _logger.info("Using Bearer token: %s...", self.token[:10] if self.token else "None")

        if fetch_all:
            final_response_json = {}
            # Fetch 200 templates at once
            # template_url += "&limit=200"
            endpoint_include = False
            while template_url:
                response = self.__api_requests("GET", url=template_url, endpoint_include=endpoint_include)
                response_json = self._safe_json_parse(response)

                if final_response_json:
                    # Add fetched data to existing response - handle Taqnyat's waba_templates structure
                    response_data = response_json.get("waba_templates", response_json.get("data", []))
                    final_response_json.setdefault("data", []).extend(response_data)
                else:
                    # Convert Taqnyat response structure to expected format
                    if "waba_templates" in response_json:
                        final_response_json = {"data": response_json["waba_templates"]}
                    else:
                        final_response_json = response_json

                # Fetch the next URL if it exists in response to fetch more templates
                template_url = response_json.get("paging", {}).get("next")
                endpoint_include = bool(template_url)
        else:
            response = self.__api_requests("GET", url=template_url)
            response_json = self._safe_json_parse(response)

            # Convert Taqnyat response structure to expected format
            if "waba_templates" in response_json:
                final_response_json = {"data": response_json["waba_templates"]}
            else:
                final_response_json = response_json

        end_time = self._get_current_time()
        template_count = len(final_response_json.get('data', []))
        _logger.info("Template sync completed for account %s [%s] at %s. Retrieved %d templates",
                    self.wa_account_id.name, self.wa_account_id.id, end_time, template_count)
        
        return final_response_json

    def _get_template_data(self, wa_template_uid):
        """
        Get single template by ID - implemented by fetching all templates
        and filtering locally since Taqnyat API doesn't support individual template retrieval

        API Documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        Note: Taqnyat API only supports GET /templates/ (all templates), not GET /templates/{id}
        """
        if self.is_shared_account:
            raise WhatsAppError(failure_type='account')

        _logger.info("Get template details for template uid %s using account %s [%s] (fetching all templates since individual retrieval not supported)",
                    wa_template_uid, self.wa_account_id.name, self.wa_account_id.id)

        # Fetch all templates since individual retrieval is not supported by Taqnyat API
        all_templates_response = self._get_all_template(fetch_all=False)
        templates = all_templates_response.get('data', [])

        # Find the specific template by ID
        for template in templates:
            if str(template.get('id')) == str(wa_template_uid):
                _logger.info("Found template %s in templates list", wa_template_uid)
                return template

        # Template not found
        _logger.warning("Template with ID '%s' not found in %d available templates", wa_template_uid, len(templates))
        raise WhatsAppError(_("Template with ID '%s' not found. It may have been deleted or the ID is incorrect.") % wa_template_uid, 'account')

    def _upload_demo_document(self, attachment):
        """
            This method is used to upload a demo document for template registration.

            API documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        """
        if self.is_shared_account:
            raise WhatsAppError(failure_type='account')

        # Taqnyat.sa uses a simpler media upload approach
        files = [('file', (attachment.name, attachment.raw, attachment.mimetype))]
        _logger.info("Upload template sample document with file size %s bytes of mimetype %s on account %s [%s]", attachment.file_size, attachment.mimetype, self.wa_account_id.name, self.wa_account_id.id)

        response = self.__api_requests("POST", "/templates/media/", files=files)
        response_json = self._safe_json_parse(response)

        # Taqnyat.sa returns media_id directly
        media_id = response_json.get('media_id')
        if not media_id:
            raise WhatsAppError(_("Document upload failed, please retry after sometime."))
        return media_id

    def _submit_template_new(self, json_data):
        """
            This method is used to submit template for approval

            API Documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        """
        if self.is_shared_account:
            raise WhatsAppError(failure_type='account')

        _logger.info("Submit new template for account %s [%s]", self.wa_account_id.name, self.wa_account_id.id)
        _logger.info("Template submission payload: %s", json_data)
        _logger.info("Using Bearer token: %s...", self.token[:10] if self.token else "None")

        response = self.__api_requests("POST", "/templates/",
                                       headers={'Content-Type': 'application/json'}, data=json_data)

        _logger.info("Template submission response status: %s", response.status_code)
        _logger.info("Template submission response headers: %s", dict(response.headers))
        _logger.info("Template submission response body: %s", response.text[:500])

        response_json = self._safe_json_parse(response)

        if response_json.get('id'):
            return {'id': response_json['id'], 'status': response_json.get('status', 'PENDING')}
        raise WhatsAppError(*self._prepare_error_response(response_json))


    def _submit_template_update(self, json_data, wa_template_uid):
        if self.is_shared_account:
            raise WhatsAppError(failure_type='account')
        _logger.info("Update template : %s for account %s [%s]", wa_template_uid, self.wa_account_id.name, self.wa_account_id.id)
        # Taqnyat.sa uses DELETE and POST for template updates
        try:
            # First delete the old template
            self.__api_requests("DELETE", f"/templates/{wa_template_uid}")
            # Then create the new one
            response = self.__api_requests("POST", "/templates/",
                                           headers={'Content-Type': 'application/json'}, data=json_data)
            response_json = self._safe_json_parse(response)

            if response_json.get('id'):
                return {'id': response_json['id'], 'status': response_json.get('status', 'PENDING')}
            return True
        except Exception:
            raise WhatsAppError(_("Template update failed"), 'account')

    def _delete_template(self, wa_template_uid):
        """Delete a template by ID (for cleanup purposes)"""
        if self.is_shared_account:
            raise WhatsAppError(failure_type='account')
        _logger.info("Delete template : %s for account %s [%s]", wa_template_uid, self.wa_account_id.name, self.wa_account_id.id)
        try:
            response = self.__api_requests("DELETE", f"/templates/{wa_template_uid}")
            return True
        except WhatsAppError:
            raise
        except Exception as e:
            _logger.error("Template deletion failed: %s", str(e))
            raise WhatsAppError(_("Template deletion failed: %s") % str(e), 'account')

    def _send_whatsapp(self, number, message_type, send_vals, parent_message_id=False):
        """ Send WA messages for all message type using Taqnyat.sa WhatsApp Business API

        API Documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        """
        # Taqnyat.sa API uses a simpler structure
        # Remove + or 00 prefix from phone number as required by Taqnyat.sa
        if number.startswith('+'):
            number = number[1:]
        elif number.startswith('00'):
            number = number[2:]

        data = {
            'to': number,
            'type': message_type
        }

        # Add message content based on type
        if message_type in ('template', 'text', 'document', 'image', 'audio', 'video'):
            data[message_type] = send_vals

        # Handle reply context for Taqnyat.sa (if supported)
        if parent_message_id:
            data['context'] = {
                'message_id': parent_message_id
            }

        json_data = json.dumps(data)
        _logger.info("Send %s message from account %s [%s]", message_type, self.wa_account_id.name, self.wa_account_id.id)
        response = self.__api_requests(
            "POST",
            "/messages/",
            headers={'Content-Type': 'application/json'},
            data=json_data
        )
        response_json = self._safe_json_parse(response)

        # Taqnyat.sa returns message_id in statuses object
        if response_json.get('statuses') and response_json['statuses'].get('message_id'):
            msg_uid = response_json['statuses']['message_id']
            return msg_uid
        raise WhatsAppError(*self._prepare_error_response(response_json))

    def __api_requests_raw(self, request_type, url, headers=None, data=None, endpoint_include=False):
        """
        Make raw API requests without JSON parsing - for fetching files/media content
        Similar to __api_requests but returns raw response without JSON parsing
        """
        headers = headers or {}

        # Taqnyat.sa authentication - handle both token formats
        # Check if token already contains "Bearer " prefix
        if self.token.startswith('Bearer '):
            headers.update({'Authorization': self.token})
        else:
            headers.update({'Authorization': f"Bearer {self.token}"})

        call_url = (DEFAULT_ENDPOINT + url) if not endpoint_include else url

        # Enhanced logging for debugging
        _logger.info("API Request: %s %s", request_type, call_url)
        _logger.info("Headers: %s", {k: v[:10] + '...' if k == 'Authorization' and len(str(v)) > 10 else v for k, v in headers.items()})

        try:
            res = requests.request(request_type, call_url, headers=headers, data=data, timeout=30)
        except requests.exceptions.Timeout:
            raise WhatsAppError(_("Request timeout. Taqnyat.sa API is not responding."), 'account')
        except requests.exceptions.ConnectionError:
            raise WhatsAppError(_("Connection error. Unable to reach Taqnyat.sa API."), 'account')
        except requests.exceptions.RequestException as e:
            raise WhatsAppError(_("Request failed: %s") % str(e), 'account')

        # Log response details
        _logger.info("API Response: Status %s, Content-Type: %s", res.status_code, res.headers.get('Content-Type', 'unknown'))

        # Handle HTTP status codes
        if res.status_code == 401:
            raise WhatsAppError(_(
                "Authentication failed (401). Please check:\n\n"
                "🔑 TOKEN ISSUES:\n"
                "• Your Bearer token may be expired or invalid\n"
                "• Token format should be: 'Bearer your_token_here'\n"
                "• Verify token has WhatsApp Business API permissions\n\n"
                "📞 NEXT STEPS:\n"
                "• Contact Taqnyat.sa support to verify your token\n"
                "• Check if your account subscription includes API access\n"
                "• Regenerate your Bearer token if needed"
            ), 'account')
        elif res.status_code == 403:
            raise WhatsAppError(_("Access forbidden (403). Your account may not have permission to access this resource."), 'account')
        elif res.status_code == 404:
            raise WhatsAppError(_("Resource not found (404). The requested URL may not exist."), 'account')
        elif res.status_code >= 500:
            raise WhatsAppError(_("Taqnyat.sa server error (Status: %s). Please try again later.") % res.status_code, 'account')
        elif not res.ok:
            raise WhatsAppError(_("Request failed with status %s") % res.status_code, 'account')

        return res

    def _get_header_data_from_handle(self, url):
        """ This method is used to get template demo document from url """
        _logger.info("Get header data for url %s from account %s [%s]", url, self.wa_account_id.name, self.wa_account_id.id)

        # Check if this is a WhatsApp-hosted media URL
        if 'scontent.whatsapp.net' in url or 'whatsapp.net' in url:
            _logger.warning("Skipping WhatsApp-hosted media URL (requires special API access): %s", url)
            # Return placeholder data for WhatsApp-hosted media
            # These URLs require authentication via WhatsApp's media API endpoints
            return b'', 'application/octet-stream'

        try:
            # Use raw request method to avoid JSON parsing for media files
            response = self.__api_requests_raw("GET", url, endpoint_include=True)
            mimetype = response.headers.get('Content-Type')
            data = response.content
            return data, mimetype
        except WhatsAppError as e:
            # Log the error but don't fail the entire sync
            _logger.warning("Failed to fetch header data from %s: %s. Continuing with template sync.", url, str(e))
            # Return placeholder data to allow sync to continue
            return b'', 'application/octet-stream'

    def _get_whatsapp_document(self, document_id):
        """
            This method is used to get document from WhatsApp sent by user

            API Documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        """
        _logger.info("Get document for document uid %s from account %s [%s]", document_id, self.wa_account_id.name, self.wa_account_id.id)
        response = self.__api_requests("GET", f"/templates/media/{document_id}")
        return response.content

    def _upload_whatsapp_document(self, attachment):
        """
            This method is used to upload document for sending via WhatsApp

            API Documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
        """
        files = [('file', (attachment.name, attachment.raw, attachment.mimetype))]
        _logger.info("Upload document of mimetype %s for account %s [%s]", attachment.mimetype, self.wa_account_id.name, self.wa_account_id.id)
        response = self.__api_requests("POST", "/media/", files=files)
        response_json = self._safe_json_parse(response)

        if response_json.get('media_id'):
            return response_json['media_id']
        raise WhatsAppError(*self._prepare_error_response(response_json))

    def _test_connection(self):
        """ This method is used to test connection of Taqnyat.sa WhatsApp Business Account"""
        _logger.info("Test connection: Testing templates endpoint for account %s [%s]", self.wa_account_id.name, self.wa_account_id.id)
        _logger.info("Using token: %s...", self.token[:10] if self.token else "None")
        _logger.info("API Base URL: %s", DEFAULT_ENDPOINT)

        try:
            # Based on Postman collection analysis, only templates endpoint is available
            # Test connection by trying to fetch templates (this is the primary API endpoint)
            _logger.info("Testing templates endpoint...")
            response = self.__api_requests("GET", "/templates/")
            _logger.info("Templates endpoint - Status: %s, Content-Type: %s",
                       response.status_code, response.headers.get('Content-Type'))

            # If __api_requests didn't raise an exception, the connection is successful
            # Additional validation: ensure we got a proper JSON response
            content_type = response.headers.get('Content-Type', '').lower()
            if 'application/json' in content_type:
                # Try to parse the response to ensure it's valid JSON
                response_json = self._safe_json_parse(response)
                # Validate that the response has the expected structure for templates
                if isinstance(response_json, dict):
                    _logger.info("Connection test successful via templates endpoint for account %s [%s]",
                               self.wa_account_id.name, self.wa_account_id.id)
                    return True
                else:
                    raise WhatsAppError(_("Invalid response structure from Taqnyat.sa API. Expected JSON object."), 'account')
            else:
                # This should not happen since __api_requests now checks for HTML responses
                raise WhatsAppError(_("Unexpected response format. Expected JSON but got: %s") % content_type, 'account')

        except WhatsAppError:
            # Re-raise WhatsAppError as-is (these are properly formatted error messages)
            raise
        except Exception as e:
            _logger.error("Connection test failed for account %s [%s]: %s", self.wa_account_id.name, self.wa_account_id.id, str(e))
            raise WhatsAppError(_("Connection test failed due to unexpected error: %s") % str(e), 'account')

    # Enhanced API Methods using new service classes

    def send_message_smart(self, phone_number, message_type, content, **kwargs):
        """
        Smart message sending that automatically determines template vs conversation
        """
        clean_phone = self.config.validate_phone_number(phone_number)

        # Check session window
        session_info = self.session_manager.get_message_type_recommendation(
            clean_phone, self.wa_account_id.id
        )

        _logger.info("Smart message for %s: session_active=%s, recommended=%s",
                    clean_phone, session_info['session_active'], session_info['recommended_type'])

        if message_type == 'text' and session_info['recommended_type'] == 'template':
            # Need to use template message instead
            template_name = kwargs.get('template_name')
            if not template_name:
                raise WhatsAppError(_("Template message required but no template specified. Session expired."), 'session')

            return self.message_service.send_template_message(
                clean_phone, template_name,
                kwargs.get('language_code', 'ar'),
                kwargs.get('components')
            )

        # Use conversation message
        if message_type == 'text':
            return self.message_service.send_text_message(
                clean_phone, content, kwargs.get('preview_url', False)
            )
        elif message_type in ['image', 'video', 'audio', 'document']:
            media_id = kwargs.get('media_id')
            if not media_id:
                raise WhatsAppError(_("Media ID required for media messages"), 'api')
            return self.message_service.send_media_message(
                clean_phone, message_type, media_id, kwargs.get('caption')
            )
        elif message_type == 'interactive':
            return self.message_service.send_interactive_message(
                clean_phone, kwargs.get('interactive_type', 'button'), content
            )
        else:
            raise WhatsAppError(_("Unsupported message type: %s") % message_type, 'api')

    def upload_media_enhanced(self, attachment):
        """
        Enhanced media upload with validation
        """
        return self.media_service.upload_media(
            attachment.raw, attachment.name, attachment.mimetype
        )

    def get_session_status(self, phone_number):
        """
        Get session window status for a phone number
        """
        clean_phone = self.config.validate_phone_number(phone_number)
        return self.session_manager.get_message_type_recommendation(
            clean_phone, self.wa_account_id.id
        )

    def get_templates_enhanced(self, limit=100, offset=0):
        """
        Enhanced template fetching with pagination
        """
        return self.template_service.get_templates(limit, offset)

    def create_template_enhanced(self, template_data):
        """
        Enhanced template creation with validation
        """
        return self.template_service.create_template(template_data)

    def get_account_statistics(self, days=7):
        """
        Get account usage statistics
        """
        return self.session_manager.get_session_statistics(self.wa_account_id.id, days)
