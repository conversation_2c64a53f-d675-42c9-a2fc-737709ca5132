#!/usr/bin/env python3
"""
Demo script for WhatsApp Global Phone Field Integration

This script demonstrates how to configure and test the global phone field integration feature.
Run this script in an Odoo shell environment to see the feature in action.

Usage:
    odoo-bin shell -d your_database --addons-path=your_addons_path
    >>> exec(open('whatsapp/tools/demo_global_integration.py').read())
"""

def demo_global_integration(env):
    """Demonstrate WhatsApp global phone field integration"""
    
    print("=" * 60)
    print("WhatsApp Global Phone Field Integration Demo")
    print("=" * 60)
    
    # 1. Check current configuration
    print("\n1. Current Configuration:")
    print("-" * 30)
    
    global_enabled = env['ir.config_parameter'].get_param('whatsapp.global_phone_integration', 'True')
    excluded_models = env['ir.config_parameter'].get_param('whatsapp.excluded_models', '')
    cache_duration = env['ir.config_parameter'].get_param('whatsapp.availability_cache_duration', '300000')
    
    print(f"Global Integration Enabled: {global_enabled}")
    print(f"Excluded Models: {excluded_models or 'None'}")
    print(f"Cache Duration: {cache_duration}ms")
    
    # 2. Check WhatsApp account configuration
    print("\n2. WhatsApp Account Status:")
    print("-" * 30)
    
    accounts = env['whatsapp.account'].search([
        ('allowed_company_ids', 'in', env.company.ids)
    ])
    
    if accounts:
        for account in accounts:
            has_taqnyat = bool(account.token)
            has_meta = bool(account.app_uid and account.app_secret and account.account_uid and account.phone_uid)
            print(f"Account: {account.name}")
            print(f"  Taqnyat.sa configured: {has_taqnyat}")
            print(f"  Meta API configured: {has_meta}")
            print(f"  Active: {account.active}")
    else:
        print("No WhatsApp accounts configured")
    
    # 3. Test availability for different models
    print("\n3. Model Availability Test:")
    print("-" * 30)
    
    test_models = [
        'res.partner',
        'crm.lead',
        'sale.order',
        'res.users',
        'hr.employee',
        'account.move'
    ]
    
    for model_name in test_models:
        try:
            can_use = env['whatsapp.template']._can_use_whatsapp(model_name)
            status = "✓ Available" if can_use else "✗ Not Available"
            print(f"  {model_name}: {status}")
        except Exception as e:
            print(f"  {model_name}: Error - {str(e)}")
    
    # 4. Demonstrate configuration changes
    print("\n4. Configuration Demo:")
    print("-" * 30)
    
    # Save original settings
    original_global = global_enabled
    original_excluded = excluded_models
    
    try:
        # Test disabling global integration
        print("Disabling global integration...")
        env['ir.config_parameter'].set_param('whatsapp.global_phone_integration', 'False')
        
        can_use_partner = env['whatsapp.template']._can_use_whatsapp('res.partner')
        print(f"  res.partner availability: {'✓' if can_use_partner else '✗'}")
        
        # Test excluding models
        print("Adding res.users to excluded models...")
        env['ir.config_parameter'].set_param('whatsapp.global_phone_integration', 'True')
        env['ir.config_parameter'].set_param('whatsapp.excluded_models', 'res.users')
        
        can_use_users = env['whatsapp.template']._can_use_whatsapp('res.users')
        can_use_partner = env['whatsapp.template']._can_use_whatsapp('res.partner')
        print(f"  res.users availability: {'✓' if can_use_users else '✗'}")
        print(f"  res.partner availability: {'✓' if can_use_partner else '✗'}")
        
    finally:
        # Restore original settings
        print("Restoring original settings...")
        env['ir.config_parameter'].set_param('whatsapp.global_phone_integration', original_global)
        env['ir.config_parameter'].set_param('whatsapp.excluded_models', original_excluded)
    
    # 5. Show configuration interface
    print("\n5. Configuration Interface:")
    print("-" * 30)
    print("To configure the global integration:")
    print("1. Go to Settings > General Settings")
    print("2. Find the 'WhatsApp' section")
    print("3. Configure 'Global Phone Field Integration' options")
    print("4. Save the configuration")
    
    # 6. Performance tips
    print("\n6. Performance Tips:")
    print("-" * 30)
    print("• Increase cache duration for high-traffic environments")
    print("• Exclude models that don't need WhatsApp integration")
    print("• Monitor API call frequency in production")
    print("• Use field-level options for fine-grained control")
    
    print("\n" + "=" * 60)
    print("Demo completed successfully!")
    print("=" * 60)


def test_field_level_options():
    """Demonstrate field-level option usage"""
    
    print("\n" + "=" * 60)
    print("Field-Level Options Demo")
    print("=" * 60)
    
    print("\nXML Examples for field-level control:")
    print("-" * 40)
    
    examples = [
        ('Enable WhatsApp (explicit)', '<field name="phone" options="{\'enable_whatsapp\': true}"/>'),
        ('Disable WhatsApp (explicit)', '<field name="phone" options="{\'enable_whatsapp\': false}"/>'),
        ('Use global setting (default)', '<field name="phone" options="{\'global_whatsapp\': true}"/>'),
        ('Disable global, enable explicit', '<field name="phone" options="{\'global_whatsapp\': false, \'enable_whatsapp\': true}"/>'),
    ]
    
    for description, example in examples:
        print(f"\n{description}:")
        print(f"  {example}")
    
    print("\nJavaScript behavior:")
    print("-" * 20)
    print("• enableWhatsAppButton: Explicit field-level control")
    print("• globalWhatsAppIntegration: Uses system-wide setting")
    print("• Button shows when: (explicit OR global) AND available AND has_phone_value")


if __name__ == '__main__':
    # This script is designed to be run in Odoo shell
    # Example: odoo-bin shell -d your_database
    # >>> exec(open('whatsapp/tools/demo_global_integration.py').read())
    
    try:
        # Try to access Odoo environment
        demo_global_integration(env)
        test_field_level_options()
    except NameError:
        print("This script must be run in an Odoo shell environment.")
        print("Usage:")
        print("  odoo-bin shell -d your_database --addons-path=your_addons_path")
        print("  >>> exec(open('whatsapp/tools/demo_global_integration.py').read())")
