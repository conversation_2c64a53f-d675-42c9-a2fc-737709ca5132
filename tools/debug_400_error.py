#!/usr/bin/env python3
"""
Debug script specifically for the 400 error issue
Tests multiple authentication and payload combinations
"""

import json
import requests
import sys
from datetime import datetime

def test_authentication_formats(token):
    """Test different authentication header formats"""
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    # Simple test payload
    test_payload = {
        "name": "debug_test_auth",
        "language": "ar",
        "allow_category_change": True,
        "category": "UTILITY",
        "components": [
            {
                "type": "BODY",
                "text": "Test message",
                "example": {
                    "body_text": [[""]]
                }
            }
        ]
    }
    
    auth_formats = [
        {
            "name": "Direct Token (Postman Collection)",
            "headers": {
                "Authorization": token,
                "Content-Type": "application/json"
            }
        },
        {
            "name": "Bearer Token",
            "headers": {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        },
        {
            "name": "Bearer with Space",
            "headers": {
                "Authorization": f"Bearer  {token}",
                "Content-Type": "application/json"
            }
        }
    ]
    
    print("🔐 TESTING AUTHENTICATION FORMATS")
    print("=" * 50)
    
    for auth_format in auth_formats:
        print(f"\n📋 Testing: {auth_format['name']}")
        print(f"Headers: {json.dumps(auth_format['headers'], indent=2)}")
        
        try:
            response = requests.post(
                f"{base_url}/templates/",
                headers=auth_format['headers'],
                data=json.dumps(test_payload),
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 401:
                print("❌ Authentication failed - Invalid token format")
            elif response.status_code == 403:
                print("❌ Forbidden - Token valid but no permissions")
            elif response.status_code == 400:
                print("⚠️  Bad Request - Authentication OK, payload issue")
                try:
                    error_json = response.json()
                    print(f"Error details: {json.dumps(error_json, indent=2)}")
                except:
                    print(f"Raw response: {response.text}")
            elif response.status_code == 201:
                print("✅ Success!")
                return auth_format['name']
            else:
                print(f"❓ Unexpected status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Network error: {e}")
    
    return None

def test_payload_formats(token, working_auth_format):
    """Test different payload formats"""
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    # Determine headers based on working auth format
    if "Direct" in working_auth_format:
        headers = {
            "Authorization": token,
            "Content-Type": "application/json"
        }
    else:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    payload_formats = [
        {
            "name": "Postman Collection Exact",
            "payload": {
                "name": "example_name",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Lorem ipsum dolor sit amet",
                        "example": {
                            "body_text": [[""]]
                        }
                    }
                ]
            }
        },
        {
            "name": "Without Example Field",
            "payload": {
                "name": "example_no_example",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Lorem ipsum dolor sit amet"
                    }
                ]
            }
        },
        {
            "name": "English Language",
            "payload": {
                "name": "example_english",
                "language": "en",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Hello world test message"
                    }
                ]
            }
        },
        {
            "name": "Marketing Category",
            "payload": {
                "name": "example_marketing",
                "language": "ar",
                "allow_category_change": True,
                "category": "MARKETING",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Marketing test message"
                    }
                ]
            }
        }
    ]
    
    print("\n📦 TESTING PAYLOAD FORMATS")
    print("=" * 50)
    
    for payload_format in payload_formats:
        print(f"\n📋 Testing: {payload_format['name']}")
        print(f"Payload: {json.dumps(payload_format['payload'], indent=2)}")
        
        try:
            response = requests.post(
                f"{base_url}/templates/",
                headers=headers,
                data=json.dumps(payload_format['payload']),
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 201:
                print("✅ Success!")
                try:
                    success_json = response.json()
                    print(f"Response: {json.dumps(success_json, indent=2)}")
                except:
                    print(f"Raw response: {response.text}")
                return payload_format['name']
            elif response.status_code == 400:
                print("❌ Bad Request")
                try:
                    error_json = response.json()
                    print(f"Error details: {json.dumps(error_json, indent=2)}")
                except:
                    print(f"Raw response: {response.text}")
            else:
                print(f"❓ Status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Network error: {e}")
    
    return None

def test_template_list(token):
    """Test if we can list templates (to verify API access)"""
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    auth_formats = [
        {"name": "Direct Token", "auth": token},
        {"name": "Bearer Token", "auth": f"Bearer {token}"}
    ]
    
    print("\n📋 TESTING TEMPLATE LIST ACCESS")
    print("=" * 50)
    
    for auth_format in auth_formats:
        print(f"\n📋 Testing: {auth_format['name']}")
        
        headers = {
            "Authorization": auth_format['auth'],
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(
                f"{base_url}/templates/",
                headers=headers,
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Template list access successful")
                try:
                    templates = response.json()
                    template_count = len(templates.get('data', []))
                    print(f"Found {template_count} templates")
                    return auth_format['name']
                except:
                    print("Response received but not JSON")
            elif response.status_code == 401:
                print("❌ Authentication failed")
            elif response.status_code == 403:
                print("❌ Access forbidden")
            else:
                print(f"❓ Status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Network error: {e}")
    
    return None

def main():
    """Main debugging function"""
    
    print("🔧 TAQNYAT.SA 400 ERROR DEBUGGER")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    
    if len(sys.argv) > 1:
        token = sys.argv[1]
    else:
        token = input("\nEnter your Bearer token: ").strip()
    
    if not token:
        print("❌ Token is required")
        return
    
    print(f"Token: {token[:10]}...")
    
    # Step 1: Test template list access
    working_list_auth = test_template_list(token)
    
    # Step 2: Test authentication formats for template creation
    working_auth = test_authentication_formats(token)
    
    if working_auth:
        print(f"\n✅ Working authentication: {working_auth}")
        
        # Step 3: Test payload formats
        working_payload = test_payload_formats(token, working_auth)
        
        if working_payload:
            print(f"\n✅ Working payload: {working_payload}")
        else:
            print("\n❌ No payload format worked")
    else:
        print("\n❌ No authentication format worked")
    
    print("\n🔧 RECOMMENDATIONS:")
    if working_list_auth and not working_auth:
        print("• Template listing works but creation fails")
        print("• Your account may not have template creation permissions")
        print("• Contact Taqnyat.sa to enable template creation API access")
    elif not working_list_auth:
        print("• Basic API access failed")
        print("• Verify your token is correct and active")
        print("• Contact Taqnyat.sa to enable API access")
    
    print("\n📞 Next Steps:")
    print("• Share these results with Taqnyat.sa support")
    print("• Request template creation API permissions")
    print("• Verify your account subscription includes API access")

if __name__ == "__main__":
    main()
