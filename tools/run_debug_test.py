#!/usr/bin/env python3
"""
Quick test runner for template submission debugging
Run this from your Odoo environment to test the API
"""

import os
import sys

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from test_template_submission import test_template_submission
    
    def main():
        print("🔧 Quick Template Submission Test")
        print("=" * 40)
        
        # You can hardcode your token here for quick testing
        # SECURITY WARNING: Remove token before committing to version control
        token = ""  # Will prompt for token input
        
        if token == "YOUR_BEARER_TOKEN_HERE":
            token = input("Enter your Bearer token: ").strip()
        
        if not token:
            print("❌ Token is required")
            return
        
        print(f"Testing with token: {token[:10]}...")
        results = test_template_submission(token)
        
        print("\n🎯 QUICK SUMMARY:")
        working_formats = [name for name, success in results.items() if success]
        if working_formats:
            print(f"✅ Working format: {working_formats[0]}")
        else:
            print("❌ No formats worked - check token and API access")
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure test_template_submission.py is in the same directory")
