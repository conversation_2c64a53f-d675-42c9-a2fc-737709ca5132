#!/usr/bin/env python3
"""
Test script to debug template submission issues with Taqnyat.sa API
This script compares different request formats to identify the working one
"""

import json
import requests
import sys
from datetime import datetime

def test_template_submission(bearer_token):
    """Test template submission with different formats"""
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    print("🚀 TAQNYAT.SA TEMPLATE SUBMISSION DEBUGGING")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print(f"Token: {bearer_token[:10]}...")
    print(f"Base URL: {base_url}")
    
    # Test cases based on your Postman collection
    test_cases = [
        {
            "name": "Postman Collection Format (Exact)",
            "headers": {
                "Authorization": bearer_token,  # Direct token, no Bearer prefix
                "Content-Type": "application/json"
            },
            "payload": {
                "name": "example_name",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Lorem ipsum dolor sit amet",
                        "example": {
                            "body_text": [[""]]  # Nested array with empty string
                        }
                    }
                ]
            }
        },
        {
            "name": "Bearer Token Format",
            "headers": {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json"
            },
            "payload": {
                "name": "example_name2",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Lorem ipsum dolor sit amet",
                        "example": {
                            "body_text": [[""]]
                        }
                    }
                ]
            }
        },
        {
            "name": "Postman Full Format (With All Components)",
            "headers": {
                "Authorization": bearer_token,
                "Content-Type": "application/json"
            },
            "payload": {
                "name": "example_name_full",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Lorem ipsum dolor sit amet",
                        "example": {
                            "body_text": [[""]]
                        }
                    },
                    {
                        "type": "HEADER",
                        "format": "TEXT",
                        "text": "Lorem ipsum"
                    },
                    {
                        "type": "FOOTER",
                        "text": "Lorem ipsum"
                    }
                ]
            }
        },
        {
            "name": "Minimal Format (No Examples)",
            "headers": {
                "Authorization": bearer_token,
                "Content-Type": "application/json"
            },
            "payload": {
                "name": "example_name_minimal",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Lorem ipsum dolor sit amet"
                    }
                ]
            }
        }
    ]
    
    results = {}
    url = f"{base_url}/templates/"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        headers = test_case['headers']
        payload = test_case['payload']
        
        print("📤 Request Details:")
        print(f"URL: {url}")
        print(f"Headers: {json.dumps(headers, indent=2)}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )
            
            print(f"\n📥 Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")
            
            # Try to parse JSON response
            try:
                response_json = response.json()
                print(f"JSON Response: {json.dumps(response_json, indent=2)}")
                
                if response.status_code == 201 and response_json.get('id'):
                    print(f"✅ {test_case['name']}: SUCCESS")
                    results[test_case['name']] = True
                elif response.status_code == 400:
                    print(f"❌ {test_case['name']}: BAD REQUEST (400)")
                    results[test_case['name']] = False
                    # Extract error details
                    if 'error' in response_json:
                        error = response_json['error']
                        print(f"Error Message: {error.get('message', 'N/A')}")
                        print(f"Error Code: {error.get('code', 'N/A')}")
                        print(f"Error Details: {error.get('details', 'N/A')}")
                else:
                    print(f"❌ {test_case['name']}: FAILED (Status: {response.status_code})")
                    results[test_case['name']] = False
                    
            except json.JSONDecodeError:
                print(f"Raw Response: {response.text}")
                print(f"❌ {test_case['name']}: INVALID JSON RESPONSE")
                results[test_case['name']] = False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {test_case['name']}: NETWORK ERROR - {str(e)}")
            results[test_case['name']] = False
    
    # Analysis
    print("\n" + "=" * 60)
    print("📊 ANALYSIS SUMMARY")
    print("=" * 60)
    
    successful_tests = [name for name, success in results.items() if success]
    failed_tests = [name for name, success in results.items() if not success]
    
    if successful_tests:
        print(f"✅ Successful formats: {', '.join(successful_tests)}")
    if failed_tests:
        print(f"❌ Failed formats: {', '.join(failed_tests)}")
    
    print("\n🔧 RECOMMENDATIONS:")
    if not any(results.values()):
        print("• All formats failed - check token validity and API access")
        print("• Verify your account has template creation permissions")
        print("• Contact Taqnyat.sa support to enable API access")
    elif "Postman Format (Exact Match)" in successful_tests:
        print("• Postman format works - update Odoo to match this format")
        print("• Focus on example field structure: use [''] instead of [['value']]")
    elif "Minimal Format (No Examples)" in successful_tests:
        print("• Remove example field from Odoo template submission")
        print("• Simplify component structure")
    
    return results

def main():
    """Main function"""
    print("🔧 Taqnyat.sa Template Submission Debugger")
    print("This script helps identify the correct API format")
    
    if len(sys.argv) > 1:
        token = sys.argv[1]
    else:
        token = input("\nEnter your Bearer token: ").strip()
    
    if not token:
        print("❌ Token is required")
        return
    
    results = test_template_submission(token)
    
    print(f"\n📋 Test completed. Results: {results}")
    
    # Generate fix recommendations
    if "Postman Format (Exact Match)" in [name for name, success in results.items() if success]:
        print("\n🛠️  ODOO FIX NEEDED:")
        print("Update the _get_template_body_component method in whatsapp_template.py:")
        print("Change: body_component['example'] = {'body_text': [demo_values]}")
        print("To:     body_component['example'] = {'body_text': demo_values}")

if __name__ == "__main__":
    main()
