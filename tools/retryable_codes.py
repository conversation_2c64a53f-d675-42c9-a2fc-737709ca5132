# Part of Odoo. See LICENSE file for full copyright and licensing details.

# Retryable error codes for Taqnyat.sa WhatsApp Business API
# Based on Taqnyat.sa API documentation: https://dev.taqnyat.sa/ar/doc/whatsapp/
WHATSAPP_RETRYABLE_ERROR_CODES = {
    # Taqnyat.sa specific error codes
    100,     # Invalid parameter or missing required parameter
    401,     # No such bot/bearer combination (authentication error)
    402,     # Invalid recipient or campaign name already used
    404,     # Template not found
    132000,  # Number of parameters does not match the expected number
    132001,  # Template name does not exist in the translation
    132012,  # Parameter format does not match format in the created template

    # Keep some common WhatsApp error codes that might still apply
    131000,  # Message failed to send due to an unknown error
    131008,  # The request is missing a required parameter
    131009,  # One or more parameter values are invalid
    131016,  # A service is temporarily unavailable
    131042,  # Message failed to send because there were one or more errors related to your payment method
    131045,  # Message failed to send due to a phone number registration error
    131048,  # Message failed to send because there are restrictions on how many messages can be sent from this phone number
    131052,  # Unable to download the media sent by the user
    131053,  # Unable to upload the media used in the message
    131056,  # Too many messages sent from the sender phone number to the same recipient phone number in a short period of time
    132015,  # Template is paused due to low quality so it cannot be sent in a template message
    132016,  # Template has been paused too many times due to low quality and is now permanently disabled
    133004,  # Server is temporarily unavailable
    133006,  # Phone number needs to be verified before registering
    133010,  # Phone number not registered on the Whatsapp Business Platform
}
