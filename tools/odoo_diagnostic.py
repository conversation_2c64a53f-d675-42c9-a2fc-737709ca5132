#!/usr/bin/env python3
"""
Odoo-integrated diagnostic tool for WhatsApp template submission issues
This can be run from within the Odoo environment to test the API
"""

import json
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

def diagnose_whatsapp_issue(wa_account):
    """
    Comprehensive diagnostic function for WhatsApp template submission issues
    
    Args:
        wa_account: whatsapp.account record
        
    Returns:
        dict: Diagnostic results with recommendations
    """
    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
    from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'account_name': wa_account.name,
        'token_length': len(wa_account.token) if wa_account.token else 0,
        'tests': {},
        'recommendations': [],
        'success': False
    }
    
    if not wa_account.token:
        results['recommendations'].append("❌ No Bearer token configured. Please add your Taqnyat.sa Bearer token.")
        return results
    
    if len(wa_account.token) < 20:
        results['recommendations'].append("⚠️ Bearer token appears too short. Verify it's complete.")
    
    # Test 1: Basic API connectivity
    try:
        wa_api = WhatsAppApi(wa_account)
        wa_api._test_connection()
        results['tests']['connectivity'] = {'status': 'success', 'message': 'API connectivity successful'}
    except WhatsAppError as e:
        error_msg = str(e)
        results['tests']['connectivity'] = {'status': 'failed', 'message': error_msg}
        
        if "Authentication failed" in error_msg:
            results['recommendations'].append("❌ Authentication failed - verify your Bearer token")
        elif "Access forbidden" in error_msg:
            results['recommendations'].append("❌ API access forbidden - contact Taqnyat.sa to enable API access")
        elif "Network error" in error_msg or "Connection error" in error_msg:
            results['recommendations'].append("❌ Network connectivity issue - check internet connection and firewall")
        else:
            results['recommendations'].append(f"❌ API connectivity failed: {error_msg}")
        return results
    except Exception as e:
        results['tests']['connectivity'] = {'status': 'error', 'message': f"Unexpected error: {str(e)}"}
        results['recommendations'].append(f"❌ Unexpected connectivity error: {str(e)}")
        return results
    
    # Test 2: Template listing (read permissions)
    try:
        response = wa_api._get_all_template(fetch_all=False)
        template_count = len(response.get('data', [])) if isinstance(response, dict) else 0
        results['tests']['template_read'] = {
            'status': 'success', 
            'message': f'Template read access successful - found {template_count} templates'
        }
    except WhatsAppError as e:
        results['tests']['template_read'] = {'status': 'failed', 'message': str(e)}
        results['recommendations'].append("⚠️ Template read access failed - may indicate API permission issues")
    except Exception as e:
        results['tests']['template_read'] = {'status': 'error', 'message': f"Unexpected error: {str(e)}"}
    
    # Test 3: Simple template creation test
    test_template_data = {
        'name': f'odoo_diagnostic_test_{int(datetime.now().timestamp())}',
        'language': 'en',
        'allow_category_change': True,
        'category': 'UTILITY',
        'components': [
            {
                'type': 'BODY',
                'text': 'This is a diagnostic test message from Odoo'
            }
        ]
    }
    
    try:
        json_data = json.dumps(test_template_data)
        response = wa_api._submit_template_new(json_data)
        results['tests']['template_creation'] = {
            'status': 'success', 
            'message': f'Template creation successful - ID: {response.get("id", "unknown")}'
        }
        results['success'] = True
        results['recommendations'].append("✅ Template creation works - the issue may be with specific template content")
        
        # Clean up test template if possible
        try:
            if response.get('id'):
                wa_api._delete_template(response['id'])
                results['tests']['cleanup'] = {'status': 'success', 'message': 'Test template cleaned up'}
        except:
            results['tests']['cleanup'] = {'status': 'failed', 'message': 'Could not clean up test template'}
            
    except WhatsAppError as e:
        error_msg = str(e)
        results['tests']['template_creation'] = {'status': 'failed', 'message': error_msg}
        
        if "Authentication failed" in error_msg:
            results['recommendations'].append("❌ Template creation failed - authentication issue")
        elif "Access forbidden" in error_msg or "permission" in error_msg.lower():
            results['recommendations'].append("❌ Template creation failed - insufficient permissions. Contact Taqnyat.sa to enable template creation API access")
        elif "Network error" in error_msg:
            results['recommendations'].append("❌ Template creation failed - network issue")
        else:
            results['recommendations'].append(f"❌ Template creation failed: {error_msg}")
    except Exception as e:
        results['tests']['template_creation'] = {'status': 'error', 'message': f"Unexpected error: {str(e)}"}
        results['recommendations'].append(f"❌ Unexpected template creation error: {str(e)}")
    
    # Final recommendations
    if results['success']:
        results['recommendations'].append("🎯 API is working correctly. Check specific template content or format.")
    else:
        results['recommendations'].append("📞 Contact Taqnyat.sa support with these diagnostic results")
    
    return results

def format_diagnostic_results(results):
    """Format diagnostic results for display"""
    output = []
    output.append("🔧 WHATSAPP API DIAGNOSTIC RESULTS")
    output.append("=" * 50)
    output.append(f"Timestamp: {results['timestamp']}")
    output.append(f"Account: {results['account_name']}")
    output.append(f"Token Length: {results['token_length']} characters")
    output.append("")
    
    output.append("📋 TEST RESULTS:")
    for test_name, test_result in results['tests'].items():
        status_icon = "✅" if test_result['status'] == 'success' else "❌" if test_result['status'] == 'failed' else "⚠️"
        output.append(f"{status_icon} {test_name.title()}: {test_result['message']}")
    
    output.append("")
    output.append("🔧 RECOMMENDATIONS:")
    for rec in results['recommendations']:
        output.append(f"  {rec}")
    
    return "\n".join(output)

def run_diagnostic_for_account(account_id):
    """
    Run diagnostic for a specific WhatsApp account ID
    This function can be called from Odoo shell or server actions
    """
    try:
        from odoo import registry, SUPERUSER_ID
        from odoo.api import Environment
        
        # This assumes we're running in an Odoo context
        env = Environment(registry(), SUPERUSER_ID, {})
        wa_account = env['whatsapp.account'].browse(account_id)
        
        if not wa_account.exists():
            return f"❌ WhatsApp account with ID {account_id} not found"
        
        results = diagnose_whatsapp_issue(wa_account)
        return format_diagnostic_results(results)
        
    except Exception as e:
        return f"❌ Error running diagnostic: {str(e)}"

# Example usage in Odoo shell:
# from odoo.addons.whatsapp.tools.odoo_diagnostic import run_diagnostic_for_account
# print(run_diagnostic_for_account(1))  # Replace 1 with your account ID
