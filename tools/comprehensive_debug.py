#!/usr/bin/env python3
"""
Comprehensive diagnostic script for WhatsApp template submission issues
This script will test all aspects of the API integration to identify the root cause
"""

import json
import requests
import sys
from datetime import datetime

def test_basic_connectivity(token):
    """Test basic API connectivity"""
    print("\n🌐 TESTING BASIC CONNECTIVITY")
    print("=" * 50)
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    # Test different authentication formats
    auth_formats = [
        {"name": "Direct Token (Postman Style)", "auth": token},
        {"name": "Bearer Token", "auth": f"Bearer {token}"}
    ]
    
    for auth_format in auth_formats:
        print(f"\n📋 Testing: {auth_format['name']}")
        
        headers = {
            "Authorization": auth_format['auth'],
            "Content-Type": "application/json"
        }
        
        try:
            # Test GET /templates/ endpoint
            response = requests.get(
                f"{base_url}/templates/",
                headers=headers,
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            
            if response.status_code == 200:
                print("✅ API connectivity successful")
                try:
                    data = response.json()
                    template_count = len(data.get('data', []))
                    print(f"Found {template_count} existing templates")
                    return auth_format['name'], auth_format['auth']
                except:
                    print("⚠️  Response not JSON format")
                    print(f"Response preview: {response.text[:200]}")
            elif response.status_code == 401:
                print("❌ Authentication failed - Invalid token")
            elif response.status_code == 403:
                print("❌ Access forbidden - Token valid but no API permissions")
            elif response.status_code == 404:
                print("❌ Endpoint not found - Check API URL")
            else:
                print(f"❓ Unexpected status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
    
    return None, None

def test_template_creation(auth_name, auth_header):
    """Test template creation with different payload formats"""
    print(f"\n📝 TESTING TEMPLATE CREATION ({auth_name})")
    print("=" * 50)
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    headers = {
        "Authorization": auth_header,
        "Content-Type": "application/json"
    }
    
    # Test different payload formats
    test_cases = [
        {
            "name": "Minimal Format (No Examples)",
            "payload": {
                "name": f"debug_minimal_{int(datetime.now().timestamp())}",
                "language": "en",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Hello world test message"
                    }
                ]
            }
        },
        {
            "name": "With Empty Example (Postman Style)",
            "payload": {
                "name": f"debug_example_{int(datetime.now().timestamp())}",
                "language": "en",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Hello world test message",
                        "example": {
                            "body_text": [[""]]
                        }
                    }
                ]
            }
        },
        {
            "name": "Arabic Language",
            "payload": {
                "name": f"debug_arabic_{int(datetime.now().timestamp())}",
                "language": "ar",
                "allow_category_change": True,
                "category": "UTILITY",
                "components": [
                    {
                        "type": "BODY",
                        "text": "رسالة تجريبية"
                    }
                ]
            }
        },
        {
            "name": "Marketing Category",
            "payload": {
                "name": f"debug_marketing_{int(datetime.now().timestamp())}",
                "language": "en",
                "allow_category_change": True,
                "category": "MARKETING",
                "components": [
                    {
                        "type": "BODY",
                        "text": "Marketing test message"
                    }
                ]
            }
        }
    ]
    
    successful_formats = []
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        print(f"Payload: {json.dumps(test_case['payload'], indent=2)}")
        
        try:
            response = requests.post(
                f"{base_url}/templates/",
                headers=headers,
                data=json.dumps(test_case['payload']),
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 201:
                print("✅ Template creation successful!")
                try:
                    success_data = response.json()
                    print(f"Template ID: {success_data.get('id', 'Unknown')}")
                    print(f"Status: {success_data.get('status', 'Unknown')}")
                    successful_formats.append(test_case['name'])
                except:
                    print(f"Raw response: {response.text}")
                    successful_formats.append(test_case['name'])
            else:
                print(f"❌ Failed with status {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"Raw response: {response.text[:300]}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
    
    return successful_formats

def analyze_current_odoo_format(auth_name, auth_header):
    """Test the exact format that Odoo is currently using"""
    print(f"\n🔍 TESTING CURRENT ODOO FORMAT ({auth_name})")
    print("=" * 50)
    
    base_url = "https://api.taqnyat.sa/wa/v2"
    
    headers = {
        "Authorization": auth_header,
        "Content-Type": "application/json"
    }
    
    # This mimics the exact format from the current Odoo implementation
    odoo_payload = {
        "name": f"testcontact_{int(datetime.now().timestamp())}",
        "language": "en",
        "allow_category_change": True,
        "category": "MARKETING",
        "components": [
            {
                "type": "BODY",
                "text": "hello {{1}} here is your order with the reference {{2}}",
                "example": {
                    "body_text": [["Mitchell Admin", "SO001"]]
                }
            }
        ]
    }
    
    print("📤 Current Odoo Format:")
    print(json.dumps(odoo_payload, indent=2))
    
    try:
        response = requests.post(
            f"{base_url}/templates/",
            headers=headers,
            data=json.dumps(odoo_payload),
            timeout=10
        )
        
        print(f"\nStatus: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Current Odoo format works!")
            try:
                success_data = response.json()
                print(f"Template ID: {success_data.get('id', 'Unknown')}")
                return True
            except:
                print(f"Raw response: {response.text}")
                return True
        else:
            print("❌ Current Odoo format failed")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw response: {response.text[:300]}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔧 COMPREHENSIVE WHATSAPP API DIAGNOSTIC")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print(f"Target API: https://api.taqnyat.sa/wa/v2")
    
    if len(sys.argv) > 1:
        token = sys.argv[1]
    else:
        token = input("\nEnter your Bearer token: ").strip()
    
    if not token:
        print("❌ Token is required")
        return
    
    print(f"Token: {token[:10]}...")
    
    # Step 1: Test basic connectivity
    auth_name, auth_header = test_basic_connectivity(token)
    
    if not auth_name:
        print("\n❌ CRITICAL: Basic API connectivity failed")
        print("🔧 RECOMMENDATIONS:")
        print("• Verify your Bearer token is correct")
        print("• Contact Taqnyat.sa to enable API access")
        print("• Check if your account subscription includes API features")
        return
    
    print(f"\n✅ API connectivity successful with: {auth_name}")
    
    # Step 2: Test template creation
    successful_formats = test_template_creation(auth_name, auth_header)
    
    # Step 3: Test current Odoo format
    odoo_works = analyze_current_odoo_format(auth_name, auth_header)
    
    # Final analysis
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if successful_formats:
        print(f"✅ Working template formats: {', '.join(successful_formats)}")
    else:
        print("❌ No template creation formats worked")
    
    if odoo_works:
        print("✅ Current Odoo format works - issue might be elsewhere")
    else:
        print("❌ Current Odoo format failed - needs modification")
    
    print("\n🔧 RECOMMENDATIONS:")
    if not successful_formats:
        print("• Template creation API access is not enabled")
        print("• Contact Taqnyat.sa support to enable template creation permissions")
        print("• Your account may only have message sending permissions")
    elif not odoo_works and successful_formats:
        print("• Template creation works but Odoo format needs adjustment")
        print("• Update Odoo to use one of the working formats")
        print(f"• Recommended format: {successful_formats[0]}")
    elif odoo_works:
        print("• API format is correct - check Odoo configuration")
        print("• Verify WhatsApp account settings in Odoo")
        print("• Check network connectivity from Odoo server")

if __name__ == "__main__":
    main()
