# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Taqnyat Message Builder
Comprehensive message building utilities for all Taqnyat message types
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.addons.whatsapp.tools.taqnyat_config import (
    MESSAGE_TYPES, INTERACTIVE_TYPES, TEMPLATE_CATEGORIES
)

_logger = logging.getLogger(__name__)

class TaqnyatMessageBuilder:
    """
    Builder class for creating Taqnyat WhatsApp messages
    """
    
    @staticmethod
    def build_text_message(text: str, preview_url: bool = False) -> Dict:
        """
        Build text message payload
        """
        if not text or not text.strip():
            raise ValidationError(_("Text message cannot be empty"))
        
        if len(text) > 4096:
            raise ValidationError(_("Text message exceeds 4096 character limit"))
        
        return {
            'body': text.strip(),
            'preview_url': preview_url
        }
    
    @staticmethod
    def build_template_message(template_name: str, language_code: str = 'ar', 
                             components: Optional[List] = None) -> Dict:
        """
        Build template message payload
        """
        if not template_name:
            raise ValidationError(_("Template name is required"))
        
        template_data = {
            'name': template_name,
            'language': {'code': language_code}
        }
        
        if components:
            template_data['components'] = components
        
        return template_data
    
    @staticmethod
    def build_media_message(media_id: str, caption: Optional[str] = None) -> Dict:
        """
        Build media message payload (image, video, audio, document)
        """
        if not media_id:
            raise ValidationError(_("Media ID is required"))
        
        media_data = {'id': media_id}
        
        if caption:
            if len(caption) > 1024:
                raise ValidationError(_("Caption exceeds 1024 character limit"))
            media_data['caption'] = caption
        
        return media_data
    
    @staticmethod
    def build_location_message(latitude: float, longitude: float, 
                             name: Optional[str] = None, address: Optional[str] = None) -> Dict:
        """
        Build location message payload
        """
        if not (-90 <= latitude <= 90):
            raise ValidationError(_("Invalid latitude value"))
        
        if not (-180 <= longitude <= 180):
            raise ValidationError(_("Invalid longitude value"))
        
        location_data = {
            'latitude': latitude,
            'longitude': longitude
        }
        
        if name:
            location_data['name'] = name
        
        if address:
            location_data['address'] = address
        
        return location_data
    
    @staticmethod
    def build_contact_message(contacts: List[Dict]) -> Dict:
        """
        Build contact message payload
        """
        if not contacts:
            raise ValidationError(_("At least one contact is required"))
        
        if len(contacts) > 5:
            raise ValidationError(_("Maximum 5 contacts allowed"))
        
        # Validate contact structure
        for contact in contacts:
            if not contact.get('name', {}).get('formatted_name'):
                raise ValidationError(_("Contact must have a formatted name"))
        
        return {'contacts': contacts}
    
    @staticmethod
    def build_interactive_button_message(body_text: str, buttons: List[Dict], 
                                       header: Optional[Dict] = None, 
                                       footer: Optional[str] = None) -> Dict:
        """
        Build interactive button message payload
        """
        if not body_text:
            raise ValidationError(_("Body text is required for button message"))
        
        if not buttons or len(buttons) == 0:
            raise ValidationError(_("At least one button is required"))
        
        if len(buttons) > 3:
            raise ValidationError(_("Maximum 3 buttons allowed"))
        
        # Validate button structure
        for i, button in enumerate(buttons):
            if not button.get('reply', {}).get('id'):
                raise ValidationError(_("Button %d must have an ID") % (i + 1))
            
            if not button.get('reply', {}).get('title'):
                raise ValidationError(_("Button %d must have a title") % (i + 1))
            
            if len(button['reply']['title']) > 20:
                raise ValidationError(_("Button %d title exceeds 20 character limit") % (i + 1))
        
        interactive_data = {
            'type': INTERACTIVE_TYPES['BUTTON'],
            'body': {'text': body_text},
            'action': {'buttons': buttons}
        }
        
        if header:
            interactive_data['header'] = header
        
        if footer:
            if len(footer) > 60:
                raise ValidationError(_("Footer exceeds 60 character limit"))
            interactive_data['footer'] = {'text': footer}
        
        return interactive_data
    
    @staticmethod
    def build_interactive_list_message(body_text: str, sections: List[Dict], 
                                     button_text: str = "Select Option",
                                     header: Optional[Dict] = None, 
                                     footer: Optional[str] = None) -> Dict:
        """
        Build interactive list message payload
        """
        if not body_text:
            raise ValidationError(_("Body text is required for list message"))
        
        if not sections or len(sections) == 0:
            raise ValidationError(_("At least one section is required"))
        
        if len(sections) > 10:
            raise ValidationError(_("Maximum 10 sections allowed"))
        
        total_rows = sum(len(section.get('rows', [])) for section in sections)
        if total_rows > 10:
            raise ValidationError(_("Maximum 10 total rows allowed across all sections"))
        
        # Validate section structure
        for i, section in enumerate(sections):
            if not section.get('rows'):
                raise ValidationError(_("Section %d must have rows") % (i + 1))
            
            for j, row in enumerate(section['rows']):
                if not row.get('id'):
                    raise ValidationError(_("Row %d in section %d must have an ID") % (j + 1, i + 1))
                
                if not row.get('title'):
                    raise ValidationError(_("Row %d in section %d must have a title") % (j + 1, i + 1))
                
                if len(row['title']) > 24:
                    raise ValidationError(_("Row %d title in section %d exceeds 24 character limit") % (j + 1, i + 1))
        
        interactive_data = {
            'type': INTERACTIVE_TYPES['LIST'],
            'body': {'text': body_text},
            'action': {
                'button': button_text,
                'sections': sections
            }
        }
        
        if header:
            interactive_data['header'] = header
        
        if footer:
            if len(footer) > 60:
                raise ValidationError(_("Footer exceeds 60 character limit"))
            interactive_data['footer'] = {'text': footer}
        
        return interactive_data

class TaqnyatTemplateBuilder:
    """
    Builder class for creating Taqnyat WhatsApp templates
    """
    
    @staticmethod
    def build_text_template(name: str, category: str, language: str, 
                          body_text: str, header_text: Optional[str] = None,
                          footer_text: Optional[str] = None, 
                          buttons: Optional[List] = None) -> Dict:
        """
        Build text template payload
        """
        if not name:
            raise ValidationError(_("Template name is required"))
        
        if category not in TEMPLATE_CATEGORIES.values():
            raise ValidationError(_("Invalid template category"))
        
        if not body_text:
            raise ValidationError(_("Body text is required"))
        
        components = []
        
        # Header component
        if header_text:
            components.append({
                'type': 'HEADER',
                'format': 'TEXT',
                'text': header_text
            })
        
        # Body component
        components.append({
            'type': 'BODY',
            'text': body_text
        })
        
        # Footer component
        if footer_text:
            components.append({
                'type': 'FOOTER',
                'text': footer_text
            })
        
        # Buttons component
        if buttons:
            components.append({
                'type': 'BUTTONS',
                'buttons': buttons
            })
        
        return {
            'name': name,
            'category': category.upper(),
            'language': language,
            'components': components
        }
    
    @staticmethod
    def build_media_template(name: str, category: str, language: str,
                           body_text: str, header_type: str, header_media_id: str,
                           footer_text: Optional[str] = None,
                           buttons: Optional[List] = None) -> Dict:
        """
        Build media template payload (image, video, document)
        """
        if header_type not in ['IMAGE', 'VIDEO', 'DOCUMENT']:
            raise ValidationError(_("Invalid header media type"))
        
        if not header_media_id:
            raise ValidationError(_("Header media ID is required"))
        
        components = []
        
        # Header component with media
        components.append({
            'type': 'HEADER',
            'format': header_type,
            'example': {
                'header_handle': [header_media_id]
            }
        })
        
        # Body component
        components.append({
            'type': 'BODY',
            'text': body_text
        })
        
        # Footer component
        if footer_text:
            components.append({
                'type': 'FOOTER',
                'text': footer_text
            })
        
        # Buttons component
        if buttons:
            components.append({
                'type': 'BUTTONS',
                'buttons': buttons
            })
        
        return {
            'name': name,
            'category': category.upper(),
            'language': language,
            'components': components
        }
    
    @staticmethod
    def build_template_button(button_type: str, text: str, 
                            url: Optional[str] = None, phone_number: Optional[str] = None) -> Dict:
        """
        Build template button
        """
        if button_type not in ['QUICK_REPLY', 'URL', 'PHONE_NUMBER']:
            raise ValidationError(_("Invalid button type"))
        
        if not text:
            raise ValidationError(_("Button text is required"))
        
        if len(text) > 25:
            raise ValidationError(_("Button text exceeds 25 character limit"))
        
        button = {
            'type': button_type,
            'text': text
        }
        
        if button_type == 'URL' and url:
            button['url'] = url
        elif button_type == 'PHONE_NUMBER' and phone_number:
            button['phone_number'] = phone_number
        
        return button
