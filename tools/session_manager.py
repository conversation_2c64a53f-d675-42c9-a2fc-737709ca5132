# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WhatsApp Session Window Manager
Manages 24-hour session windows for conversation vs template message logic
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.addons.whatsapp.tools.taqnyat_config import TaqnyatConfig

_logger = logging.getLogger(__name__)

class SessionWindowManager:
    """
    Manages WhatsApp 24-hour session windows
    Determines when to use template vs conversation messages
    """
    
    def __init__(self, env):
        self.env = env
        self.config = TaqnyatConfig()
        self.session_window_hours = self.config.get_session_window_hours()
    
    def is_session_active(self, phone_number: str, wa_account_id: int) -> bool:
        """
        Check if there's an active session window for the phone number
        """
        # Clean phone number
        clean_phone = self.config.validate_phone_number(phone_number)
        
        # Get the last inbound message from this contact
        last_inbound = self._get_last_inbound_message(clean_phone, wa_account_id)
        
        if not last_inbound:
            return False
        
        # Check if within 24-hour window
        cutoff_time = datetime.now() - timedelta(hours=self.session_window_hours)
        return last_inbound.create_date >= cutoff_time
    
    def get_session_expiry(self, phone_number: str, wa_account_id: int) -> Optional[datetime]:
        """
        Get session expiry time for the phone number
        """
        clean_phone = self.config.validate_phone_number(phone_number)
        last_inbound = self._get_last_inbound_message(clean_phone, wa_account_id)
        
        if not last_inbound:
            return None
        
        return last_inbound.create_date + timedelta(hours=self.session_window_hours)
    
    def get_session_remaining_time(self, phone_number: str, wa_account_id: int) -> Optional[timedelta]:
        """
        Get remaining time in current session window
        """
        expiry = self.get_session_expiry(phone_number, wa_account_id)
        if not expiry:
            return None
        
        remaining = expiry - datetime.now()
        return remaining if remaining.total_seconds() > 0 else None
    
    def should_use_template(self, phone_number: str, wa_account_id: int) -> bool:
        """
        Determine if template message should be used instead of conversation message
        """
        return not self.is_session_active(phone_number, wa_account_id)
    
    def get_message_type_recommendation(self, phone_number: str, wa_account_id: int) -> Dict[str, Any]:
        """
        Get comprehensive recommendation for message type
        """
        clean_phone = self.config.validate_phone_number(phone_number)
        is_active = self.is_session_active(clean_phone, wa_account_id)
        
        result = {
            'phone_number': clean_phone,
            'session_active': is_active,
            'recommended_type': 'conversation' if is_active else 'template',
            'session_expiry': self.get_session_expiry(clean_phone, wa_account_id),
            'remaining_time': self.get_session_remaining_time(clean_phone, wa_account_id)
        }
        
        # Add human-readable remaining time
        if result['remaining_time']:
            total_seconds = int(result['remaining_time'].total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            result['remaining_time_human'] = f"{hours}h {minutes}m"
        else:
            result['remaining_time_human'] = "Expired"
        
        return result
    
    def _get_last_inbound_message(self, phone_number: str, wa_account_id: int):
        """
        Get the last inbound message from the contact
        """
        # Search for the most recent inbound message
        domain = [
            ('wa_account_id', '=', wa_account_id),
            ('mobile_number', '=', phone_number),
            ('message_type', '=', 'inbound')
        ]
        
        return self.env['whatsapp.message'].search(domain, order='create_date desc', limit=1)
    
    def update_session_on_inbound_message(self, phone_number: str, wa_account_id: int, message_id: str):
        """
        Update session tracking when receiving an inbound message
        """
        clean_phone = self.config.validate_phone_number(phone_number)
        
        # Log session update
        _logger.info("Session window updated for %s (account %d) - message %s", 
                    clean_phone, wa_account_id, message_id)
        
        # The session is automatically updated by the message creation timestamp
        # Additional tracking can be implemented here if needed
        
        return True
    
    def get_session_statistics(self, wa_account_id: int, days: int = 7) -> Dict[str, Any]:
        """
        Get session statistics for the account
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # Get all messages in the period
        domain = [
            ('wa_account_id', '=', wa_account_id),
            ('create_date', '>=', cutoff_date)
        ]
        
        messages = self.env['whatsapp.message'].search(domain)
        
        # Analyze session patterns
        active_sessions = set()
        total_inbound = 0
        total_outbound = 0
        
        for message in messages:
            if message.message_type == 'inbound':
                total_inbound += 1
                # Check if this creates an active session
                if self.is_session_active(message.mobile_number, wa_account_id):
                    active_sessions.add(message.mobile_number)
            else:
                total_outbound += 1
        
        return {
            'period_days': days,
            'total_messages': len(messages),
            'inbound_messages': total_inbound,
            'outbound_messages': total_outbound,
            'active_sessions': len(active_sessions),
            'unique_contacts': len(set(msg.mobile_number for msg in messages if msg.mobile_number))
        }

class SessionWindowMixin(models.AbstractModel):
    """
    Mixin to add session window functionality to models
    """
    _name = 'whatsapp.session.mixin'
    _description = 'WhatsApp Session Window Mixin'
    
    def _get_session_manager(self):
        """
        Get session manager instance
        """
        return SessionWindowManager(self.env)
    
    def check_session_window(self, phone_number: str, wa_account_id: int) -> Dict[str, Any]:
        """
        Check session window status for a contact
        """
        manager = self._get_session_manager()
        return manager.get_message_type_recommendation(phone_number, wa_account_id)
    
    def is_conversation_allowed(self, phone_number: str, wa_account_id: int) -> bool:
        """
        Check if conversation messages are allowed (within session window)
        """
        manager = self._get_session_manager()
        return manager.is_session_active(phone_number, wa_account_id)
    
    def get_session_info(self, phone_number: str, wa_account_id: int) -> str:
        """
        Get human-readable session information
        """
        session_info = self.check_session_window(phone_number, wa_account_id)
        
        if session_info['session_active']:
            return _("Session active - expires in %s") % session_info['remaining_time_human']
        else:
            return _("No active session - template message required")

# Utility functions for session management
def get_session_window_status(env, phone_number: str, wa_account_id: int) -> Dict[str, Any]:
    """
    Utility function to get session window status
    """
    manager = SessionWindowManager(env)
    return manager.get_message_type_recommendation(phone_number, wa_account_id)

def is_template_required(env, phone_number: str, wa_account_id: int) -> bool:
    """
    Utility function to check if template message is required
    """
    manager = SessionWindowManager(env)
    return manager.should_use_template(phone_number, wa_account_id)

def update_session_on_message(env, phone_number: str, wa_account_id: int, message_id: str, message_type: str):
    """
    Utility function to update session on message events
    """
    if message_type == 'inbound':
        manager = SessionWindowManager(env)
        manager.update_session_on_inbound_message(phone_number, wa_account_id, message_id)
