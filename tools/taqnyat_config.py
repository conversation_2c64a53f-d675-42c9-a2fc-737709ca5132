# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Taqnyat WhatsApp Business API Configuration
Centralized configuration management for Taqnyat.sa API integration
"""

import logging
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

# Taqnyat API Configuration Constants
TAQNYAT_API_BASE_URL = "https://api.taqnyat.sa/wa/v2"
TAQNYAT_PORTAL_URL = "https://portal.taqnyat.sa"

# Message Type Constants
MESSAGE_TYPES = {
    'TEMPLATE': 'template',
    'TEXT': 'text',
    'IMAGE': 'image',
    'VIDEO': 'video',
    'AUDIO': 'audio',
    'DOCUMENT': 'document',
    'LOCATION': 'location',
    'CONTACTS': 'contacts',
    'INTERACTIVE': 'interactive',
    'STICKER': 'sticker'
}

# Template Categories
TEMPLATE_CATEGORIES = {
    'MARKETING': 'marketing',
    'UTILITY': 'utility',
    'AUTHENTICATION': 'authentication'
}

# Message Status Constants
MESSAGE_STATUSES = {
    'QUEUED': 'queued',
    'DISPATCHED': 'dispatched',
    'SENT': 'sent',
    'DELIVERED': 'delivered',
    'READ': 'read',
    'FAILED': 'failed',
    'DELETED': 'deleted'
}

# Template Status Constants
TEMPLATE_STATUSES = {
    'PENDING': 'pending',
    'APPROVED': 'approved',
    'REJECTED': 'rejected',
    'DISABLED': 'disabled'
}

# Interactive Message Types
INTERACTIVE_TYPES = {
    'LIST': 'list',
    'BUTTON': 'button',
    'PRODUCT': 'product',
    'PRODUCT_LIST': 'product_list'
}

# Media Upload Limits (in bytes)
MEDIA_LIMITS = {
    'image': 5 * 1024 * 1024,  # 5MB
    'video': 16 * 1024 * 1024,  # 16MB
    'audio': 16 * 1024 * 1024,  # 16MB
    'document': 100 * 1024 * 1024,  # 100MB
    'sticker': 500 * 1024  # 500KB
}

# Supported Media Types
SUPPORTED_MEDIA_TYPES = {
    'image': ['image/jpeg', 'image/png', 'image/webp'],
    'video': ['video/mp4', 'video/3gpp'],
    'audio': ['audio/aac', 'audio/mp4', 'audio/mpeg', 'audio/amr', 'audio/ogg'],
    'document': ['application/pdf', 'application/vnd.ms-powerpoint', 'application/msword',
                'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    'sticker': ['image/webp']
}

# Error Codes and Retry Logic
RETRYABLE_ERROR_CODES = [429, 500, 502, 503, 504]
NON_RETRYABLE_ERROR_CODES = [400, 401, 403, 404]

# Session Window Configuration
SESSION_WINDOW_HOURS = 24  # 24-hour session window for conversation messages

# Rate Limiting Configuration
RATE_LIMIT_CONFIG = {
    'messages_per_second': 20,
    'templates_per_minute': 100,
    'media_uploads_per_minute': 50
}

# Webhook Configuration
WEBHOOK_EVENTS = {
    'MESSAGE_STATUS': 'message_status',
    'INBOUND_MESSAGE': 'inbound_message',
    'TEMPLATE_STATUS': 'template_status'
}

class TaqnyatConfig:
    """
    Configuration utility class for Taqnyat WhatsApp Business API
    Provides centralized access to API configuration and validation
    """
    
    @staticmethod
    def get_api_base_url():
        """Get the base URL for Taqnyat API"""
        return TAQNYAT_API_BASE_URL
    
    @staticmethod
    def get_portal_url():
        """Get the portal URL for Taqnyat"""
        return TAQNYAT_PORTAL_URL
    
    @staticmethod
    def validate_phone_number(phone_number):
        """
        Validate phone number format for Taqnyat API
        Must be in international format without + or 00 prefix
        """
        if not phone_number:
            raise ValidationError(_("Phone number is required"))
        
        # Remove common prefixes
        clean_number = phone_number
        if clean_number.startswith('+'):
            clean_number = clean_number[1:]
        elif clean_number.startswith('00'):
            clean_number = clean_number[2:]
        
        # Validate format (should be digits only, 10-15 characters)
        if not clean_number.isdigit():
            raise ValidationError(_("Phone number must contain only digits"))
        
        if len(clean_number) < 10 or len(clean_number) > 15:
            raise ValidationError(_("Phone number must be between 10 and 15 digits"))
        
        return clean_number
    
    @staticmethod
    def validate_media_file(file_data, media_type, file_size):
        """
        Validate media file for upload
        """
        if media_type not in MEDIA_LIMITS:
            raise ValidationError(_("Unsupported media type: %s") % media_type)
        
        if file_size > MEDIA_LIMITS[media_type]:
            max_size_mb = MEDIA_LIMITS[media_type] / (1024 * 1024)
            raise ValidationError(_("File size exceeds limit of %.1f MB for %s") % (max_size_mb, media_type))
        
        return True
    
    @staticmethod
    def validate_media_mimetype(mimetype, media_type):
        """
        Validate media MIME type
        """
        if media_type not in SUPPORTED_MEDIA_TYPES:
            raise ValidationError(_("Unsupported media type: %s") % media_type)
        
        if mimetype not in SUPPORTED_MEDIA_TYPES[media_type]:
            supported = ', '.join(SUPPORTED_MEDIA_TYPES[media_type])
            raise ValidationError(_("Unsupported MIME type %s for %s. Supported types: %s") % (mimetype, media_type, supported))
        
        return True
    
    @staticmethod
    def is_retryable_error(status_code):
        """
        Check if an error status code is retryable
        """
        return status_code in RETRYABLE_ERROR_CODES
    
    @staticmethod
    def get_session_window_hours():
        """
        Get the session window duration in hours
        """
        return SESSION_WINDOW_HOURS
    
    @staticmethod
    def get_rate_limits():
        """
        Get rate limiting configuration
        """
        return RATE_LIMIT_CONFIG.copy()
    
    @staticmethod
    def get_webhook_events():
        """
        Get supported webhook events
        """
        return WEBHOOK_EVENTS.copy()

# Configuration validation functions
def validate_bearer_token(token):
    """
    Validate Bearer token format
    """
    if not token:
        raise ValidationError(_("Bearer token is required"))
    
    if len(token) < 10:
        raise ValidationError(_("Bearer token appears to be too short"))
    
    # Additional validation can be added here
    return True

def get_default_headers(token):
    """
    Get default headers for API requests
    """
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

def build_api_url(endpoint):
    """
    Build complete API URL from endpoint
    """
    base_url = TAQNYAT_API_BASE_URL.rstrip('/')
    endpoint = endpoint.lstrip('/')
    return f"{base_url}/{endpoint}"
