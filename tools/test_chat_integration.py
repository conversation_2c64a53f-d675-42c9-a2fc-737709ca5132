#!/usr/bin/env python3
"""
Test script to verify the WhatsApp chat integration functionality
This can be run from Odoo shell to test the new chat features
"""

def test_whatsapp_chat_integration():
    """
    Test the WhatsApp chat integration features
    Run this from Odoo shell:
    
    from odoo.addons.whatsapp.tools.test_chat_integration import test_whatsapp_chat_integration
    test_whatsapp_chat_integration()
    """
    try:
        from odoo import registry, SUPERUSER_ID
        from odoo.api import Environment
        
        # Get Odoo environment
        env = Environment(registry(), SUPERUSER_ID, {})
        
        print("🔧 Testing WhatsApp Chat Integration")
        print("=" * 50)
        
        # Test 1: Check WhatsApp account availability
        print("\n📱 Test 1: Checking WhatsApp account availability...")
        wa_account = env['whatsapp.account'].search([], limit=1)
        if not wa_account:
            print("❌ No WhatsApp account found. Please create one first.")
            return False
        
        print(f"✅ Found WhatsApp account: {wa_account.name}")
        print(f"🔑 Token configured: {'Yes' if wa_account.token else 'No'}")
        
        # Test 2: Check if WhatsApp is available globally
        print("\n🌐 Test 2: Checking global WhatsApp availability...")
        is_available = env['whatsapp.account'].is_whatsapp_available()
        print(f"✅ WhatsApp globally available: {is_available}")
        
        # Test 3: Test direct message model
        print("\n📝 Test 3: Testing WhatsApp direct message model...")
        try:
            # Create a test partner
            test_partner = env['res.partner'].create({
                'name': 'WhatsApp Test Contact',
                'mobile': '+************',
                'email': '<EMAIL>',
            })
            print(f"✅ Created test partner: {test_partner.name}")
            
            # Test direct message creation
            direct_message = env['whatsapp.direct.message'].create({
                'phone_number': test_partner.mobile,
                'message_body': 'This is a test message from Odoo chat integration',
                'wa_account_id': wa_account.id,
                'partner_id': test_partner.id,
                'thread_model': 'res.partner',
                'thread_id': test_partner.id,
            })
            print(f"✅ Created direct message record: {direct_message.id}")
            
            # Test phone number formatting
            formatted_phone = direct_message._format_phone_number()
            print(f"✅ Phone number formatting: {direct_message.phone_number} → {formatted_phone}")
            
            # Clean up test data
            direct_message.unlink()
            test_partner.unlink()
            print("✅ Test data cleaned up")
            
        except Exception as e:
            print(f"❌ Direct message model test failed: {str(e)}")
            return False
        
        # Test 4: Test WhatsApp API direct message method
        print("\n🌐 Test 4: Testing WhatsApp API direct message method...")
        try:
            from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
            
            if not wa_account.token:
                print("⚠️ Skipping API test - no Bearer token configured")
            else:
                wa_api = WhatsAppApi(wa_account)
                print("✅ WhatsApp API initialized successfully")
                print("✅ Direct message method available:", hasattr(wa_api, 'send_direct_message'))
                
        except Exception as e:
            print(f"❌ WhatsApp API test failed: {str(e)}")
            return False
        
        # Test 5: Test partner WhatsApp integration
        print("\n👤 Test 5: Testing partner WhatsApp integration...")
        try:
            # Create a test partner with phone
            test_partner = env['res.partner'].create({
                'name': 'WhatsApp Integration Test',
                'mobile': '+************',
                'phone': '+************',
            })
            
            # Test partner has phone numbers
            has_mobile = bool(test_partner.mobile)
            has_phone = bool(test_partner.phone)
            print(f"✅ Partner mobile: {has_mobile}")
            print(f"✅ Partner phone: {has_phone}")
            
            # Test default values for direct message
            defaults = env['whatsapp.direct.message'].with_context(
                partner_id=test_partner.id
            ).default_get(['phone_number', 'partner_id'])
            
            print(f"✅ Default phone from partner: {defaults.get('phone_number', 'None')}")
            print(f"✅ Default partner ID: {defaults.get('partner_id', 'None')}")
            
            # Clean up
            test_partner.unlink()
            
        except Exception as e:
            print(f"❌ Partner integration test failed: {str(e)}")
            return False
        
        # Test 6: Test security access
        print("\n🔒 Test 6: Testing security access...")
        try:
            # Check if user can access direct message model
            can_read = env['whatsapp.direct.message'].check_access_rights('read', raise_exception=False)
            can_create = env['whatsapp.direct.message'].check_access_rights('create', raise_exception=False)
            can_write = env['whatsapp.direct.message'].check_access_rights('write', raise_exception=False)
            
            print(f"✅ Can read direct messages: {can_read}")
            print(f"✅ Can create direct messages: {can_create}")
            print(f"✅ Can write direct messages: {can_write}")
            
        except Exception as e:
            print(f"❌ Security access test failed: {str(e)}")
            return False
        
        # Test 7: Test view access
        print("\n👁️ Test 7: Testing view access...")
        try:
            # Check if views exist
            form_view = env.ref('whatsapp.whatsapp_direct_message_view_form', raise_if_not_found=False)
            action = env.ref('whatsapp.whatsapp_direct_message_action', raise_if_not_found=False)
            quick_action = env.ref('whatsapp.whatsapp_direct_message_quick_action', raise_if_not_found=False)
            
            print(f"✅ Form view exists: {bool(form_view)}")
            print(f"✅ Action exists: {bool(action)}")
            print(f"✅ Quick action exists: {bool(quick_action)}")
            
        except Exception as e:
            print(f"❌ View access test failed: {str(e)}")
            return False
        
        print("\n🎉 All tests passed successfully!")
        print("\n📋 Integration Summary:")
        print("✅ WhatsApp direct messaging model created")
        print("✅ Security access configured")
        print("✅ Views and actions available")
        print("✅ Partner integration working")
        print("✅ API integration ready")
        
        print("\n🚀 Next Steps:")
        print("1. Test the UI by opening a partner record")
        print("2. Click the 'Send WhatsApp Message' button")
        print("3. Try the chat interface with Ctrl+Shift+W shortcut")
        print("4. Verify messages are sent and tracked properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_whatsapp_ui_integration():
    """Test UI integration components"""
    try:
        from odoo import registry, SUPERUSER_ID
        from odoo.api import Environment
        
        env = Environment(registry(), SUPERUSER_ID, {})
        
        print("\n🖥️ Testing UI Integration Components")
        print("=" * 50)
        
        # Test JavaScript assets
        print("📁 Checking JavaScript assets...")
        assets_to_check = [
            'whatsapp/static/src/discuss/core/common/composer_patch.js',
            'whatsapp/static/src/discuss/core/common/composer_patch.xml',
            'whatsapp/static/src/core/common/store_service_patch.js',
            'whatsapp/static/src/scss/whatsapp_composer.scss',
        ]
        
        import os
        addon_path = os.path.dirname(os.path.dirname(__file__))
        
        for asset in assets_to_check:
            asset_path = os.path.join(addon_path, asset.replace('whatsapp/', ''))
            exists = os.path.exists(asset_path)
            print(f"{'✅' if exists else '❌'} {asset}: {'Found' if exists else 'Missing'}")
        
        print("\n✅ UI integration components check completed")
        return True
        
    except Exception as e:
        print(f"❌ UI integration test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script should be run from Odoo shell:")
    print("from odoo.addons.whatsapp.tools.test_chat_integration import test_whatsapp_chat_integration")
    print("test_whatsapp_chat_integration()")
