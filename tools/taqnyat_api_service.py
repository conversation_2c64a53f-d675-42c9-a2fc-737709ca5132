# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Taqnyat WhatsApp Business API Service
Core service classes for comprehensive Taqnyat.sa API integration
"""

import json
import logging
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from odoo.addons.whatsapp.tools.whatsapp_exception import WhatsAppError
from odoo.addons.whatsapp.tools.taqnyat_config import (
    TaqnyatConfig, MESSAGE_TYPES, MESSAGE_STATUSES, TEMPLATE_STATUSES,
    INTERACTIVE_TYPES, get_default_headers, build_api_url, validate_bearer_token
)

_logger = logging.getLogger(__name__)

class TaqnyatApiService:
    """
    Core API service for Taqnyat WhatsApp Business API
    Handles authentication, request management, and error handling
    """
    
    def __init__(self, wa_account):
        """
        Initialize the API service with WhatsApp account
        """
        self.wa_account = wa_account
        self.token = wa_account.sudo().token
        self.config = TaqnyatConfig()
        self._validate_configuration()
    
    def _validate_configuration(self):
        """
        Validate API configuration
        """
        if not self.token:
            raise WhatsAppError(_("Bearer token is required for Taqnyat API"), 'account')
        
        validate_bearer_token(self.token)
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     files: Optional[Dict] = None, params: Optional[Dict] = None,
                     timeout: int = 30, retry_count: int = 3) -> requests.Response:
        """
        Make HTTP request to Taqnyat API with retry logic and error handling
        """
        url = build_api_url(endpoint)
        headers = get_default_headers(self.token)
        
        # Remove Content-Type for file uploads
        if files:
            headers.pop('Content-Type', None)
        
        for attempt in range(retry_count + 1):
            try:
                _logger.info("API Request [Attempt %d/%d]: %s %s", attempt + 1, retry_count + 1, method, url)
                
                response = requests.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data if data and not files else None,
                    data=data if files else None,
                    files=files,
                    params=params,
                    timeout=timeout
                )
                
                _logger.info("API Response: Status %d, Content-Type: %s", 
                           response.status_code, response.headers.get('Content-Type', 'Unknown'))
                
                # Handle successful responses
                if response.ok:
                    return response
                
                # Handle retryable errors
                if self.config.is_retryable_error(response.status_code) and attempt < retry_count:
                    wait_time = 2 ** attempt  # Exponential backoff
                    _logger.warning("Retryable error %d, waiting %d seconds before retry", 
                                  response.status_code, wait_time)
                    time.sleep(wait_time)
                    continue
                
                # Handle non-retryable errors
                self._handle_error_response(response)
                
            except requests.exceptions.Timeout:
                if attempt < retry_count:
                    _logger.warning("Request timeout, retrying...")
                    time.sleep(2 ** attempt)
                    continue
                raise WhatsAppError(_("Request timeout after %d attempts") % (retry_count + 1), 'network')
            
            except requests.exceptions.ConnectionError:
                if attempt < retry_count:
                    _logger.warning("Connection error, retrying...")
                    time.sleep(2 ** attempt)
                    continue
                raise WhatsAppError(_("Connection error after %d attempts") % (retry_count + 1), 'network')
            
            except requests.exceptions.RequestException as e:
                _logger.error("Request exception: %s", str(e))
                raise WhatsAppError(_("Network error: %s") % str(e), 'network')
        
        # This should not be reached, but just in case
        raise WhatsAppError(_("Maximum retry attempts exceeded"), 'network')
    
    def _handle_error_response(self, response: requests.Response):
        """
        Handle error responses from Taqnyat API
        """
        status_code = response.status_code
        
        # Standard HTTP error handling
        if status_code == 401:
            raise WhatsAppError(_("Authentication failed. Invalid Bearer token."), 'account')
        elif status_code == 403:
            raise WhatsAppError(_("Access forbidden. Token lacks required permissions."), 'account')
        elif status_code == 404:
            raise WhatsAppError(_("API endpoint not found."), 'account')
        elif status_code == 429:
            raise WhatsAppError(_("Rate limit exceeded. Please try again later."), 'rate_limit')
        elif status_code >= 500:
            raise WhatsAppError(_("Server error (Status: %d). Please try again later.") % status_code, 'server')
        
        # Try to parse error details from response
        try:
            error_data = response.json()
            error_message = self._extract_error_message(error_data)
            raise WhatsAppError(error_message, 'api')
        except (json.JSONDecodeError, ValueError):
            # Fallback to raw response
            raise WhatsAppError(_("API error (Status: %d): %s") % (status_code, response.text[:200]), 'api')
    
    def _extract_error_message(self, error_data: Dict) -> str:
        """
        Extract error message from API response
        """
        if isinstance(error_data, dict):
            # Common error fields
            for field in ['message', 'error', 'detail', 'description']:
                if field in error_data:
                    return str(error_data[field])
            
            # Nested error structures
            if 'error' in error_data and isinstance(error_data['error'], dict):
                return self._extract_error_message(error_data['error'])
        
        return _("Unknown API error")
    
    def test_connection(self) -> bool:
        """
        Test API connection by fetching templates
        """
        try:
            response = self._make_request('GET', '/templates/', params={'limit': 1})
            return response.ok
        except WhatsAppError:
            raise
        except Exception as e:
            _logger.error("Connection test failed: %s", str(e))
            raise WhatsAppError(_("Connection test failed: %s") % str(e), 'network')

class TaqnyatMessageService(TaqnyatApiService):
    """
    Service for sending messages through Taqnyat API
    """
    
    def send_template_message(self, phone_number: str, template_name: str, 
                            language_code: str = 'ar', components: Optional[List] = None) -> str:
        """
        Send template message
        """
        clean_phone = self.config.validate_phone_number(phone_number)
        
        message_data = {
            'to': clean_phone,
            'type': MESSAGE_TYPES['TEMPLATE'],
            'template': {
                'name': template_name,
                'language': {'code': language_code}
            }
        }
        
        if components:
            message_data['template']['components'] = components
        
        response = self._make_request('POST', '/messages/', data=message_data)
        response_data = response.json()
        
        # Extract message ID from response
        if 'statuses' in response_data and response_data['statuses']:
            return response_data['statuses'][0].get('message_id')
        
        raise WhatsAppError(_("Failed to send template message"), 'api')
    
    def send_text_message(self, phone_number: str, text: str, 
                         preview_url: bool = False) -> str:
        """
        Send text message (conversation)
        """
        clean_phone = self.config.validate_phone_number(phone_number)
        
        message_data = {
            'to': clean_phone,
            'type': MESSAGE_TYPES['TEXT'],
            'text': {
                'body': text,
                'preview_url': preview_url
            }
        }
        
        response = self._make_request('POST', '/messages/', data=message_data)
        response_data = response.json()
        
        if 'statuses' in response_data and response_data['statuses']:
            return response_data['statuses'][0].get('message_id')
        
        raise WhatsAppError(_("Failed to send text message"), 'api')
    
    def send_media_message(self, phone_number: str, media_type: str, 
                          media_id: str, caption: Optional[str] = None) -> str:
        """
        Send media message (image, video, audio, document)
        """
        if media_type not in [MESSAGE_TYPES['IMAGE'], MESSAGE_TYPES['VIDEO'], 
                             MESSAGE_TYPES['AUDIO'], MESSAGE_TYPES['DOCUMENT']]:
            raise ValidationError(_("Unsupported media type: %s") % media_type)
        
        clean_phone = self.config.validate_phone_number(phone_number)
        
        media_data = {'id': media_id}
        if caption and media_type in [MESSAGE_TYPES['IMAGE'], MESSAGE_TYPES['VIDEO'], MESSAGE_TYPES['DOCUMENT']]:
            media_data['caption'] = caption
        
        message_data = {
            'to': clean_phone,
            'type': media_type,
            media_type: media_data
        }
        
        response = self._make_request('POST', '/messages/', data=message_data)
        response_data = response.json()
        
        if 'statuses' in response_data and response_data['statuses']:
            return response_data['statuses'][0].get('message_id')
        
        raise WhatsAppError(_("Failed to send media message"), 'api')
    
    def send_interactive_message(self, phone_number: str, interactive_type: str, 
                               interactive_data: Dict) -> str:
        """
        Send interactive message (buttons, lists)
        """
        if interactive_type not in INTERACTIVE_TYPES.values():
            raise ValidationError(_("Unsupported interactive type: %s") % interactive_type)
        
        clean_phone = self.config.validate_phone_number(phone_number)
        
        message_data = {
            'to': clean_phone,
            'type': MESSAGE_TYPES['INTERACTIVE'],
            'interactive': {
                'type': interactive_type,
                **interactive_data
            }
        }
        
        response = self._make_request('POST', '/messages/', data=message_data)
        response_data = response.json()
        
        if 'statuses' in response_data and response_data['statuses']:
            return response_data['statuses'][0].get('message_id')
        
        raise WhatsAppError(_("Failed to send interactive message"), 'api')

class TaqnyatMediaService(TaqnyatApiService):
    """
    Service for media upload and management
    """

    def upload_media(self, file_data: bytes, filename: str, mimetype: str) -> str:
        """
        Upload media file and return media ID
        """
        # Determine media type from mimetype
        media_type = self._get_media_type_from_mimetype(mimetype)

        # Validate file
        file_size = len(file_data)
        self.config.validate_media_file(file_data, media_type, file_size)
        self.config.validate_media_mimetype(mimetype, media_type)

        # Prepare file for upload
        files = {
            'file': (filename, file_data, mimetype)
        }

        _logger.info("Uploading media file: %s (%s, %d bytes)", filename, mimetype, file_size)

        response = self._make_request('POST', '/media/', files=files)
        response_data = response.json()

        media_id = response_data.get('media_id')
        if not media_id:
            raise WhatsAppError(_("Media upload failed - no media ID returned"), 'api')

        _logger.info("Media uploaded successfully: %s", media_id)
        return media_id

    def get_media_url(self, media_id: str) -> str:
        """
        Get media download URL
        """
        response = self._make_request('GET', f'/media/{media_id}')
        response_data = response.json()

        media_url = response_data.get('url')
        if not media_url:
            raise WhatsAppError(_("Failed to get media URL"), 'api')

        return media_url

    def download_media(self, media_id: str) -> bytes:
        """
        Download media content
        """
        media_url = self.get_media_url(media_id)

        # Download the actual media content
        response = requests.get(media_url, timeout=30)
        response.raise_for_status()

        return response.content

    def _get_media_type_from_mimetype(self, mimetype: str) -> str:
        """
        Determine media type from MIME type
        """
        if mimetype.startswith('image/'):
            return 'image'
        elif mimetype.startswith('video/'):
            return 'video'
        elif mimetype.startswith('audio/'):
            return 'audio'
        elif mimetype.startswith('application/') or mimetype.startswith('text/'):
            return 'document'
        else:
            raise ValidationError(_("Unsupported MIME type: %s") % mimetype)

class TaqnyatTemplateService(TaqnyatApiService):
    """
    Service for template management
    """

    def get_templates(self, limit: int = 100, offset: int = 0) -> Dict:
        """
        Get list of templates - handles Taqnyat's waba_templates response structure
        """
        params = {
            'limit': limit,
            'offset': offset
        }

        response = self._make_request('GET', '/templates/', params=params)
        response_data = response.json()

        # Convert Taqnyat response structure to expected format
        if "waba_templates" in response_data:
            return {"data": response_data["waba_templates"]}
        else:
            return response_data

    def get_template(self, template_id: str) -> Dict:
        """
        Get specific template details - implemented by fetching all templates
        and filtering locally since Taqnyat API doesn't support individual template retrieval
        """
        # Fetch all templates since individual retrieval is not supported
        all_templates = self.get_templates(limit=1000)  # Get a large number to ensure we find it
        templates = all_templates.get('data', [])

        # Find the specific template by ID
        for template in templates:
            if str(template.get('id')) == str(template_id):
                return template

        # Template not found
        raise WhatsAppError(f"Template with ID '{template_id}' not found. It may have been deleted or the ID is incorrect.", 'account')

    def create_template(self, template_data: Dict) -> Dict:
        """
        Create new template
        """
        response = self._make_request('POST', '/templates/', data=template_data)
        response_data = response.json()

        if not response_data.get('id'):
            raise WhatsAppError(_("Template creation failed"), 'api')

        return response_data

    def delete_template(self, template_id: str) -> bool:
        """
        Delete template
        """
        response = self._make_request('DELETE', f'/templates/{template_id}')
        return response.ok

    def upload_template_media(self, file_data: bytes, filename: str, mimetype: str) -> str:
        """
        Upload media for template (demo document)
        """
        files = {
            'file': (filename, file_data, mimetype)
        }

        response = self._make_request('POST', '/templates/media/', files=files)
        response_data = response.json()

        media_id = response_data.get('media_id')
        if not media_id:
            raise WhatsAppError(_("Template media upload failed"), 'api')

        return media_id
