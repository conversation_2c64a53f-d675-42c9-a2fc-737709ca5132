# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WhatsApp Module Indexing Test Script

This script can be used to test the indexing improvements manually.
Run this from Odoo shell or as a standalone script.

Usage:
    # From Odoo shell
    from odoo.addons.whatsapp.tools.test_indexing import test_indexing_system
    test_indexing_system(env)
    
    # Or run specific tests
    from odoo.addons.whatsapp.tools.test_indexing import *
    test_database_indexes(env)
    test_performance_tracking(env)
"""

import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


def test_indexing_system(env):
    """
    Comprehensive test of the WhatsApp indexing system
    
    Args:
        env: Odoo environment
        
    Returns:
        dict: Test results summary
    """
    _logger.info("Starting WhatsApp indexing system test...")
    
    results = {
        'database_indexes': test_database_indexes(env),
        'performance_tracking': test_performance_tracking(env),
        'search_optimization': test_search_optimization(env),
        'cron_jobs': test_cron_jobs(env),
        'performance_views': test_performance_views(env),
    }
    
    # Summary
    total_tests = sum(len(result['tests']) for result in results.values())
    passed_tests = sum(sum(1 for test in result['tests'] if test['passed']) for result in results.values())
    
    _logger.info("WhatsApp indexing test completed: %d/%d tests passed", passed_tests, total_tests)
    
    results['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
    }
    
    return results


def test_database_indexes(env):
    """Test that database indexes are properly created"""
    _logger.info("Testing database indexes...")
    
    tests = []
    
    # List of expected indexes
    expected_indexes = [
        'whatsapp_account_token_partial_idx',
        'whatsapp_account_active_company_idx',
        'whatsapp_account_search_keywords_idx',
        'whatsapp_account_api_monitoring_idx',
        'whatsapp_account_sync_status_idx',
        'whatsapp_message_account_state_idx',
        'whatsapp_message_mobile_account_idx',
        'whatsapp_message_msg_uid_idx',
        'whatsapp_message_template_idx',
        'whatsapp_message_delivery_tracking_idx',
        'whatsapp_template_account_status_idx',
        'whatsapp_template_uid_idx',
        'whatsapp_template_name_lang_idx',
        'whatsapp_template_variable_template_idx',
        'whatsapp_template_button_template_idx',
        'discuss_channel_whatsapp_idx',
        'mail_message_whatsapp_idx',
    ]
    
    for index_name in expected_indexes:
        try:
            env.cr.execute("SELECT 1 FROM pg_indexes WHERE indexname = %s", (index_name,))
            result = env.cr.fetchone()
            passed = bool(result)
            tests.append({
                'name': f'Index {index_name}',
                'passed': passed,
                'message': 'Found' if passed else 'Not found'
            })
        except Exception as e:
            tests.append({
                'name': f'Index {index_name}',
                'passed': False,
                'message': f'Error: {str(e)}'
            })
    
    # Test performance view
    try:
        env.cr.execute("SELECT 1 FROM pg_views WHERE viewname = 'whatsapp_performance_stats'")
        result = env.cr.fetchone()
        passed = bool(result)
        tests.append({
            'name': 'Performance stats view',
            'passed': passed,
            'message': 'Found' if passed else 'Not found'
        })
    except Exception as e:
        tests.append({
            'name': 'Performance stats view',
            'passed': False,
            'message': f'Error: {str(e)}'
        })
    
    return {'tests': tests}


def test_performance_tracking(env):
    """Test performance tracking functionality"""
    _logger.info("Testing performance tracking...")
    
    tests = []
    
    try:
        # Test account performance fields
        WhatsAppAccount = env['whatsapp.account']
        
        # Create a test account
        test_account = WhatsAppAccount.create({
            'name': 'Test Indexing Account',
            'token': 'test_indexing_token_123456789',
        })
        
        # Test that performance fields are initialized
        tests.append({
            'name': 'Account performance fields initialization',
            'passed': (test_account.last_api_reset is not False and 
                      test_account.api_call_count == 0),
            'message': 'Performance fields properly initialized'
        })
        
        # Test API counter increment
        initial_count = test_account.api_call_count
        test_account._increment_api_counter()
        tests.append({
            'name': 'API counter increment',
            'passed': test_account.api_call_count == initial_count + 1,
            'message': f'Counter incremented from {initial_count} to {test_account.api_call_count}'
        })
        
        # Test search keywords computation
        test_account._compute_search_keywords()
        tests.append({
            'name': 'Search keywords computation',
            'passed': bool(test_account.search_keywords and 'taqnyat' in test_account.search_keywords.lower()),
            'message': f'Keywords: {test_account.search_keywords[:50]}...'
        })
        
        # Clean up
        test_account.unlink()
        
    except Exception as e:
        tests.append({
            'name': 'Performance tracking test',
            'passed': False,
            'message': f'Error: {str(e)}'
        })
    
    try:
        # Test message performance fields
        WhatsAppMessage = env['whatsapp.message']
        
        # Check that message model has performance fields
        field_names = WhatsAppMessage._fields.keys()
        has_send_duration = 'send_duration' in field_names
        has_delivery_duration = 'delivery_duration' in field_names
        
        tests.append({
            'name': 'Message performance fields',
            'passed': has_send_duration and has_delivery_duration,
            'message': f'send_duration: {has_send_duration}, delivery_duration: {has_delivery_duration}'
        })
        
    except Exception as e:
        tests.append({
            'name': 'Message performance fields test',
            'passed': False,
            'message': f'Error: {str(e)}'
        })
    
    return {'tests': tests}


def test_search_optimization(env):
    """Test search optimization features"""
    _logger.info("Testing search optimization...")
    
    tests = []
    
    try:
        WhatsAppAccount = env['whatsapp.account']
        
        # Test search by keywords (if any accounts exist)
        accounts = WhatsAppAccount.search([], limit=5)
        
        if accounts:
            # Test that search keywords are computed
            for account in accounts:
                if not account.search_keywords:
                    account._compute_search_keywords()
            
            # Test search functionality
            keyword_search_count = len([acc for acc in accounts if acc.search_keywords])
            
            tests.append({
                'name': 'Search keywords generation',
                'passed': keyword_search_count > 0,
                'message': f'{keyword_search_count}/{len(accounts)} accounts have search keywords'
            })
        else:
            tests.append({
                'name': 'Search keywords generation',
                'passed': True,
                'message': 'No accounts to test (skipped)'
            })
            
    except Exception as e:
        tests.append({
            'name': 'Search optimization test',
            'passed': False,
            'message': f'Error: {str(e)}'
        })
    
    return {'tests': tests}


def test_cron_jobs(env):
    """Test that performance-related cron jobs exist and are active"""
    _logger.info("Testing cron jobs...")
    
    tests = []
    
    expected_crons = [
        ('whatsapp.ir_cron_reset_whatsapp_api_counters', 'API Counter Reset'),
        ('whatsapp.ir_cron_cleanup_whatsapp_performance', 'Performance Cleanup'),
    ]
    
    for cron_xmlid, cron_name in expected_crons:
        try:
            cron = env.ref(cron_xmlid, raise_if_not_found=False)
            if cron:
                tests.append({
                    'name': f'Cron job: {cron_name}',
                    'passed': cron.active,
                    'message': f'Active: {cron.active}, Interval: {cron.interval_number} {cron.interval_type}'
                })
            else:
                tests.append({
                    'name': f'Cron job: {cron_name}',
                    'passed': False,
                    'message': 'Cron job not found'
                })
        except Exception as e:
            tests.append({
                'name': f'Cron job: {cron_name}',
                'passed': False,
                'message': f'Error: {str(e)}'
            })
    
    return {'tests': tests}


def test_performance_views(env):
    """Test that performance views are accessible"""
    _logger.info("Testing performance views...")
    
    tests = []
    
    expected_actions = [
        ('whatsapp.whatsapp_performance_dashboard_action', 'Performance Dashboard'),
        ('whatsapp.whatsapp_account_performance_action', 'Account Performance'),
    ]
    
    for action_xmlid, action_name in expected_actions:
        try:
            action = env.ref(action_xmlid, raise_if_not_found=False)
            tests.append({
                'name': f'Action: {action_name}',
                'passed': bool(action),
                'message': f'Model: {action.res_model}' if action else 'Action not found'
            })
        except Exception as e:
            tests.append({
                'name': f'Action: {action_name}',
                'passed': False,
                'message': f'Error: {str(e)}'
            })
    
    return {'tests': tests}


def print_test_results(results):
    """Print test results in a readable format"""
    print("\n" + "="*60)
    print("WhatsApp Module Indexing Test Results")
    print("="*60)
    
    for category, result in results.items():
        if category == 'summary':
            continue
            
        print(f"\n{category.upper().replace('_', ' ')}:")
        print("-" * 40)
        
        for test in result['tests']:
            status = "✅ PASS" if test['passed'] else "❌ FAIL"
            print(f"{status} {test['name']}: {test['message']}")
    
    if 'summary' in results:
        summary = results['summary']
        print(f"\n{'='*60}")
        print(f"SUMMARY: {summary['passed_tests']}/{summary['total_tests']} tests passed ({summary['success_rate']:.1f}%)")
        print("="*60)


if __name__ == '__main__':
    print("This script should be run from within Odoo environment.")
    print("Example usage:")
    print("  from odoo.addons.whatsapp.tools.test_indexing import test_indexing_system, print_test_results")
    print("  results = test_indexing_system(env)")
    print("  print_test_results(results)")
