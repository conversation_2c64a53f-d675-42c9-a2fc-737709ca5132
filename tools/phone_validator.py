# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Enhanced Phone Number Validation and Opt-in Management
Comprehensive phone number validation and consent management for Taqnyat WhatsApp API
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from odoo.addons.whatsapp.tools.taqnyat_config import TaqnyatConfig

_logger = logging.getLogger(__name__)

class PhoneNumberValidator:
    """
    Enhanced phone number validation for international formats
    """
    
    # Country code patterns for common regions
    COUNTRY_PATTERNS = {
        'SA': r'^966[0-9]{8,9}$',  # Saudi Arabia
        'AE': r'^971[0-9]{8,9}$',  # UAE
        'EG': r'^20[0-9]{9,10}$',  # Egypt
        'JO': r'^962[0-9]{8,9}$',  # Jordan
        'KW': r'^965[0-9]{7,8}$',  # Kuwait
        'QA': r'^974[0-9]{7,8}$',  # Qatar
        'BH': r'^973[0-9]{7,8}$',  # Bahrain
        'OM': r'^968[0-9]{7,8}$',  # Oman
        'LB': r'^961[0-9]{7,8}$',  # Lebanon
        'SY': r'^963[0-9]{8,9}$',  # Syria
        'IQ': r'^964[0-9]{9,10}$', # Iraq
        'YE': r'^967[0-9]{8,9}$',  # Yemen
        'PS': r'^970[0-9]{8,9}$',  # Palestine
        'MA': r'^212[0-9]{8,9}$',  # Morocco
        'TN': r'^216[0-9]{7,8}$',  # Tunisia
        'DZ': r'^213[0-9]{8,9}$',  # Algeria
        'LY': r'^218[0-9]{8,9}$',  # Libya
        'SD': r'^249[0-9]{8,9}$',  # Sudan
    }
    
    @staticmethod
    def clean_phone_number(phone_number: str) -> str:
        """
        Clean and normalize phone number
        """
        if not phone_number:
            raise ValidationError(_("Phone number is required"))
        
        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number.strip())
        
        # Remove leading + or 00
        if cleaned.startswith('+'):
            cleaned = cleaned[1:]
        elif cleaned.startswith('00'):
            cleaned = cleaned[2:]
        
        return cleaned
    
    @staticmethod
    def validate_phone_format(phone_number: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate phone number format and detect country
        Returns: (is_valid, cleaned_number, country_code)
        """
        try:
            cleaned = PhoneNumberValidator.clean_phone_number(phone_number)
            
            # Basic length validation
            if len(cleaned) < 10 or len(cleaned) > 15:
                return False, cleaned, None
            
            # Check if it's all digits
            if not cleaned.isdigit():
                return False, cleaned, None
            
            # Try to detect country
            detected_country = PhoneNumberValidator.detect_country(cleaned)
            
            # Validate against country patterns if detected
            if detected_country:
                pattern = PhoneNumberValidator.COUNTRY_PATTERNS.get(detected_country)
                if pattern and re.match(pattern, cleaned):
                    return True, cleaned, detected_country
            
            # Fallback: if no specific country pattern matches but format is reasonable
            if 10 <= len(cleaned) <= 15:
                return True, cleaned, None
            
            return False, cleaned, None
            
        except Exception as e:
            _logger.error("Error validating phone number %s: %s", phone_number, str(e))
            return False, phone_number, None
    
    @staticmethod
    def detect_country(phone_number: str) -> Optional[str]:
        """
        Detect country from phone number
        """
        for country, pattern in PhoneNumberValidator.COUNTRY_PATTERNS.items():
            if re.match(pattern, phone_number):
                return country
        return None
    
    @staticmethod
    def format_for_display(phone_number: str) -> str:
        """
        Format phone number for display
        """
        cleaned = PhoneNumberValidator.clean_phone_number(phone_number)
        return f"+{cleaned}"
    
    @staticmethod
    def validate_for_taqnyat(phone_number: str) -> str:
        """
        Validate and format phone number for Taqnyat API
        """
        is_valid, cleaned, country = PhoneNumberValidator.validate_phone_format(phone_number)
        
        if not is_valid:
            raise ValidationError(_("Invalid phone number format: %s") % phone_number)
        
        return cleaned

class OptInManager:
    """
    Manage opt-in/opt-out status for WhatsApp contacts
    """
    
    def __init__(self, env):
        self.env = env
    
    def check_opt_in_status(self, phone_number: str, wa_account_id: int) -> Dict[str, Any]:
        """
        Check opt-in status for a phone number
        """
        cleaned_phone = PhoneNumberValidator.validate_for_taqnyat(phone_number)
        
        # Look for existing opt-in record
        opt_in = self.env['whatsapp.optin'].search([
            ('mobile_number', '=', cleaned_phone),
            ('wa_account_id', '=', wa_account_id)
        ], limit=1)
        
        if opt_in:
            return {
                'opted_in': opt_in.is_opted_in,
                'opt_in_date': opt_in.opt_in_date,
                'opt_out_date': opt_in.opt_out_date,
                'source': opt_in.source,
                'can_send_marketing': opt_in.is_opted_in and opt_in.allow_marketing,
                'can_send_utility': True,  # Utility messages generally allowed
                'last_updated': opt_in.write_date
            }
        else:
            return {
                'opted_in': False,
                'opt_in_date': None,
                'opt_out_date': None,
                'source': None,
                'can_send_marketing': False,
                'can_send_utility': True,  # Utility messages for new contacts
                'last_updated': None
            }
    
    def record_opt_in(self, phone_number: str, wa_account_id: int, 
                     source: str = 'manual', allow_marketing: bool = True) -> bool:
        """
        Record opt-in for a phone number
        """
        cleaned_phone = PhoneNumberValidator.validate_for_taqnyat(phone_number)
        
        # Find or create opt-in record
        opt_in = self.env['whatsapp.optin'].search([
            ('mobile_number', '=', cleaned_phone),
            ('wa_account_id', '=', wa_account_id)
        ], limit=1)
        
        if opt_in:
            opt_in.write({
                'is_opted_in': True,
                'opt_in_date': datetime.now(),
                'opt_out_date': False,
                'source': source,
                'allow_marketing': allow_marketing
            })
        else:
            opt_in = self.env['whatsapp.optin'].create({
                'mobile_number': cleaned_phone,
                'wa_account_id': wa_account_id,
                'is_opted_in': True,
                'opt_in_date': datetime.now(),
                'source': source,
                'allow_marketing': allow_marketing
            })
        
        _logger.info("Recorded opt-in for %s (account %d, source: %s)", 
                    cleaned_phone, wa_account_id, source)
        return True
    
    def record_opt_out(self, phone_number: str, wa_account_id: int, 
                      reason: str = 'user_request') -> bool:
        """
        Record opt-out for a phone number
        """
        cleaned_phone = PhoneNumberValidator.validate_for_taqnyat(phone_number)
        
        # Find existing opt-in record
        opt_in = self.env['whatsapp.optin'].search([
            ('mobile_number', '=', cleaned_phone),
            ('wa_account_id', '=', wa_account_id)
        ], limit=1)
        
        if opt_in:
            opt_in.write({
                'is_opted_in': False,
                'opt_out_date': datetime.now(),
                'opt_out_reason': reason
            })
        else:
            # Create opt-out record even if no previous opt-in
            opt_in = self.env['whatsapp.optin'].create({
                'mobile_number': cleaned_phone,
                'wa_account_id': wa_account_id,
                'is_opted_in': False,
                'opt_out_date': datetime.now(),
                'opt_out_reason': reason
            })
        
        _logger.info("Recorded opt-out for %s (account %d, reason: %s)", 
                    cleaned_phone, wa_account_id, reason)
        return True
    
    def can_send_message(self, phone_number: str, wa_account_id: int, 
                        message_category: str = 'utility') -> Tuple[bool, str]:
        """
        Check if we can send a message to this number
        """
        status = self.check_opt_in_status(phone_number, wa_account_id)
        
        if message_category == 'marketing':
            if not status['can_send_marketing']:
                return False, "Contact has not opted in for marketing messages"
        
        # Utility and authentication messages are generally allowed
        # unless explicitly opted out
        if not status['opted_in'] and status['opt_out_date']:
            return False, "Contact has explicitly opted out"
        
        return True, "Message allowed"
    
    def bulk_opt_in(self, phone_numbers: List[str], wa_account_id: int, 
                   source: str = 'bulk_import') -> Dict[str, Any]:
        """
        Bulk opt-in for multiple phone numbers
        """
        results = {
            'success': [],
            'failed': [],
            'total': len(phone_numbers)
        }
        
        for phone in phone_numbers:
            try:
                self.record_opt_in(phone, wa_account_id, source)
                results['success'].append(phone)
            except Exception as e:
                results['failed'].append({'phone': phone, 'error': str(e)})
        
        return results
    
    def get_opt_in_statistics(self, wa_account_id: int, days: int = 30) -> Dict[str, Any]:
        """
        Get opt-in statistics for the account
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        domain_base = [('wa_account_id', '=', wa_account_id)]
        
        total_contacts = self.env['whatsapp.optin'].search_count(domain_base)
        opted_in = self.env['whatsapp.optin'].search_count(
            domain_base + [('is_opted_in', '=', True)]
        )
        opted_out = self.env['whatsapp.optin'].search_count(
            domain_base + [('is_opted_in', '=', False)]
        )
        
        recent_opt_ins = self.env['whatsapp.optin'].search_count(
            domain_base + [
                ('is_opted_in', '=', True),
                ('opt_in_date', '>=', cutoff_date)
            ]
        )
        
        recent_opt_outs = self.env['whatsapp.optin'].search_count(
            domain_base + [
                ('is_opted_in', '=', False),
                ('opt_out_date', '>=', cutoff_date)
            ]
        )
        
        return {
            'total_contacts': total_contacts,
            'opted_in': opted_in,
            'opted_out': opted_out,
            'opt_in_rate': (opted_in / total_contacts * 100) if total_contacts > 0 else 0,
            'recent_opt_ins': recent_opt_ins,
            'recent_opt_outs': recent_opt_outs,
            'period_days': days
        }

# Utility functions
def validate_phone_number(phone_number: str) -> str:
    """
    Utility function to validate phone number
    """
    return PhoneNumberValidator.validate_for_taqnyat(phone_number)

def check_can_send_message(env, phone_number: str, wa_account_id: int, 
                          message_category: str = 'utility') -> Tuple[bool, str]:
    """
    Utility function to check if message can be sent
    """
    manager = OptInManager(env)
    return manager.can_send_message(phone_number, wa_account_id, message_category)
