# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
import os

from . import controller
from . import models
from . import tools
from . import wizard
from . import populate

_logger = logging.getLogger(__name__)


def post_init_hook(env):
    """
    Post-installation hook to apply database indexes and initialize performance tracking
    """
    try:
        _logger.info("Starting WhatsApp module indexing improvements...")

        # Apply database indexes
        _apply_database_indexes(env)

        # Initialize performance tracking fields
        _initialize_performance_tracking(env)

        # Reset API counters
        _reset_api_counters(env)

        _logger.info("WhatsApp module indexing improvements completed successfully")

    except Exception as e:
        _logger.error("Error applying WhatsApp indexing improvements: %s", str(e), exc_info=True)


def _apply_database_indexes(env):
    """Apply database indexes for performance optimization"""
    try:
        # Get the path to the SQL file
        module_path = os.path.dirname(__file__)
        sql_file_path = os.path.join(module_path, 'data', 'whatsapp_indexes.sql')

        if os.path.exists(sql_file_path):
            _logger.info("Applying WhatsApp database indexes...")

            # Read and execute the SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as sql_file:
                sql_content = sql_file.read()

            # Remove comments and split by statements
            lines = []
            for line in sql_content.split('\n'):
                line = line.strip()
                if line and not line.startswith('--'):
                    lines.append(line)

            sql_clean = ' '.join(lines)
            statements = [stmt.strip() for stmt in sql_clean.split(';') if stmt.strip()]

            success_count = 0
            warning_count = 0

            for statement in statements:
                if statement.upper().startswith(('CREATE INDEX', 'CREATE OR REPLACE VIEW')):
                    try:
                        env.cr.execute(statement + ';')
                        success_count += 1
                        _logger.debug("Successfully executed: %s", statement[:100] + "...")
                    except Exception as e:
                        warning_count += 1
                        # Log but don't fail - indexes might already exist
                        _logger.warning("Index creation warning (may already exist): %s", str(e))

            env.cr.commit()
            _logger.info("Database indexes applied: %d successful, %d warnings", success_count, warning_count)
        else:
            _logger.warning("SQL indexes file not found: %s", sql_file_path)

    except Exception as e:
        _logger.error("Error applying database indexes: %s", str(e), exc_info=True)


def _initialize_performance_tracking(env):
    """Initialize performance tracking fields for existing records"""
    try:
        _logger.info("Initializing performance tracking fields...")

        # Update existing WhatsApp accounts with default values
        accounts = env['whatsapp.account'].search([])
        updated_count = 0

        for account in accounts:
            update_vals = {}

            if not account.last_api_reset:
                update_vals['last_api_reset'] = env.cr.now().date()

            if account.api_call_count is False or account.api_call_count is None:
                update_vals['api_call_count'] = 0

            if update_vals:
                account.write(update_vals)
                updated_count += 1

        _logger.info("Performance tracking fields initialized for %d/%d accounts", updated_count, len(accounts))

        # Trigger search keywords computation for all accounts
        if accounts:
            accounts._compute_search_keywords()
            _logger.info("Search keywords computed for all accounts")

    except Exception as e:
        _logger.error("Error initializing performance tracking: %s", str(e), exc_info=True)


def _reset_api_counters(env):
    """Reset API counters to start fresh"""
    try:
        _logger.info("Resetting API counters...")

        # Reset all API counters
        env['whatsapp.account'].search([]).write({
            'api_call_count': 0,
            'last_api_reset': env.cr.now().date()
        })

        _logger.info("API counters reset successfully")

    except Exception as e:
        _logger.error("Error resetting API counters: %s", str(e), exc_info=True)
