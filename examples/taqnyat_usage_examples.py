# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Taqnyat WhatsApp Integration Usage Examples
Comprehensive examples demonstrating all features of the enhanced integration
"""

import logging
from datetime import datetime

# Example usage scripts for Taqnyat WhatsApp integration

def example_basic_message_sending(env):
    """
    Example: Basic message sending with session management
    """
    print("=== Basic Message Sending Example ===")
    
    # Get WhatsApp account
    wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
    if not wa_account:
        print("No active WhatsApp account found")
        return
    
    # Initialize API
    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
    api = WhatsAppApi(wa_account)
    
    # Example phone number (Saudi Arabia)
    phone_number = "************"
    
    try:
        # Check session status
        session_status = api.get_session_status(phone_number)
        print(f"Session Status: {session_status}")
        
        # Send smart message (automatically chooses template vs conversation)
        if session_status['session_active']:
            # Send conversation message
            result = api.send_message_smart(
                phone_number=phone_number,
                message_type='text',
                content="Hello! How can I help you today?",
                preview_url=True
            )
        else:
            # Send template message
            result = api.send_message_smart(
                phone_number=phone_number,
                message_type='text',
                content="",  # Content comes from template
                template_name='hello_world',
                language_code='ar',
                components=[
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": "Ahmed"}
                        ]
                    }
                ]
            )
        
        print(f"Message sent successfully: {result}")
        
    except Exception as e:
        print(f"Error sending message: {e}")

def example_interactive_messages(env):
    """
    Example: Sending interactive messages (buttons and lists)
    """
    print("=== Interactive Messages Example ===")
    
    wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
    if not wa_account:
        return
    
    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
    from odoo.addons.whatsapp.tools.message_builder import TaqnyatMessageBuilder
    
    api = WhatsAppApi(wa_account)
    phone_number = "************"
    
    try:
        # Button message example
        buttons = [
            {"reply": {"id": "yes_btn", "title": "Yes"}},
            {"reply": {"id": "no_btn", "title": "No"}},
            {"reply": {"id": "maybe_btn", "title": "Maybe"}}
        ]
        
        button_message = TaqnyatMessageBuilder.build_interactive_button_message(
            body_text="Would you like to receive updates about your order?",
            buttons=buttons,
            header={"type": "text", "text": "Order Updates"},
            footer="Reply with your preference"
        )
        
        result = api.send_message_smart(
            phone_number=phone_number,
            message_type='interactive',
            content=button_message,
            interactive_type='button'
        )
        print(f"Button message sent: {result}")
        
        # List message example
        sections = [
            {
                "title": "Main Menu",
                "rows": [
                    {"id": "menu_1", "title": "View Products", "description": "Browse our catalog"},
                    {"id": "menu_2", "title": "Track Order", "description": "Check order status"},
                    {"id": "menu_3", "title": "Support", "description": "Get help"}
                ]
            }
        ]
        
        list_message = TaqnyatMessageBuilder.build_interactive_list_message(
            body_text="How can we help you today?",
            sections=sections,
            button_text="Select Option",
            header={"type": "text", "text": "Customer Service"}
        )
        
        result = api.send_message_smart(
            phone_number=phone_number,
            message_type='interactive',
            content=list_message,
            interactive_type='list'
        )
        print(f"List message sent: {result}")
        
    except Exception as e:
        print(f"Error sending interactive message: {e}")

def example_media_handling(env):
    """
    Example: Media upload and sending
    """
    print("=== Media Handling Example ===")
    
    wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
    if not wa_account:
        return
    
    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
    
    api = WhatsAppApi(wa_account)
    phone_number = "************"
    
    try:
        # Example: Upload and send image
        # Note: In real usage, you would have actual file data
        # This is just demonstrating the API structure
        
        # Simulate attachment object
        class MockAttachment:
            def __init__(self):
                self.raw = b"fake_image_data"
                self.name = "product_image.jpg"
                self.mimetype = "image/jpeg"
        
        attachment = MockAttachment()
        
        # Upload media
        media_result = api.upload_media_enhanced(attachment)
        print(f"Media uploaded: {media_result}")
        
        if media_result.get('media_id'):
            # Send media message
            result = api.send_message_smart(
                phone_number=phone_number,
                message_type='image',
                content="",
                media_id=media_result['media_id'],
                caption="Check out our new product!"
            )
            print(f"Media message sent: {result}")
        
    except Exception as e:
        print(f"Error handling media: {e}")

def example_opt_in_management(env):
    """
    Example: Managing opt-in/opt-out for contacts
    """
    print("=== Opt-in Management Example ===")
    
    wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
    if not wa_account:
        return
    
    from odoo.addons.whatsapp.tools.phone_validator import OptInManager
    
    manager = OptInManager(env)
    phone_numbers = ["************", "************", "************"]
    
    try:
        # Bulk opt-in
        bulk_result = manager.bulk_opt_in(phone_numbers, wa_account.id, 'example_script')
        print(f"Bulk opt-in result: {bulk_result}")
        
        # Check individual opt-in status
        for phone in phone_numbers:
            status = manager.check_opt_in_status(phone, wa_account.id)
            print(f"Phone {phone} status: {status}")
            
            # Check if we can send marketing messages
            can_send, reason = manager.can_send_message(phone, wa_account.id, 'marketing')
            print(f"Can send marketing to {phone}: {can_send} - {reason}")
        
        # Get statistics
        stats = manager.get_opt_in_statistics(wa_account.id, 30)
        print(f"Opt-in statistics: {stats}")
        
    except Exception as e:
        print(f"Error managing opt-ins: {e}")

def example_template_management(env):
    """
    Example: Template creation and management
    """
    print("=== Template Management Example ===")
    
    wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
    if not wa_account:
        return
    
    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
    from odoo.addons.whatsapp.tools.message_builder import TaqnyatTemplateBuilder
    
    api = WhatsAppApi(wa_account)
    
    try:
        # Create a new template
        template_data = TaqnyatTemplateBuilder.build_text_template(
            name="order_confirmation",
            category="utility",
            language="ar",
            body_text="تم تأكيد طلبك رقم {{1}}. سيتم التوصيل خلال {{2}} أيام عمل.",
            header_text="تأكيد الطلب",
            footer_text="شكراً لاختيارك متجرنا",
            buttons=[
                TaqnyatTemplateBuilder.build_template_button(
                    "QUICK_REPLY", "تتبع الطلب"
                ),
                TaqnyatTemplateBuilder.build_template_button(
                    "URL", "زيارة الموقع", "https://example.com"
                )
            ]
        )
        
        result = api.create_template_enhanced(template_data)
        print(f"Template created: {result}")
        
        # Get existing templates
        templates = api.get_templates_enhanced(limit=10)
        print(f"Existing templates: {templates}")
        
    except Exception as e:
        print(f"Error managing templates: {e}")

def example_webhook_simulation(env):
    """
    Example: Simulating webhook processing
    """
    print("=== Webhook Processing Example ===")
    
    # Simulate incoming webhook data
    webhook_data = {
        "notifications": [
            {
                "from": "************",
                "to": "************",
                "timestamp": "1640995200",
                "message": {
                    "id": "wamid.example123",
                    "type": "text",
                    "text": {
                        "body": "Hello, I need help with my order"
                    }
                }
            }
        ],
        "statuses": [
            {
                "message_id": "wamid.example456",
                "status": "delivered",
                "recipient_id": "************",
                "timestamp": "1640995300"
            }
        ]
    }
    
    try:
        # Process webhook (this would normally be called by the webhook controller)
        from odoo.addons.whatsapp.controller.main import WhatsAppController
        controller = WhatsAppController()
        
        # This demonstrates the webhook processing structure
        print("Webhook data structure:")
        print(f"Notifications: {len(webhook_data.get('notifications', []))}")
        print(f"Status updates: {len(webhook_data.get('statuses', []))}")
        
        # In real usage, this would be processed by the controller
        # result = controller._process_taqnyat_webhook(webhook_data)
        # print(f"Webhook processed: {result}")
        
    except Exception as e:
        print(f"Error processing webhook: {e}")

def example_comprehensive_workflow(env):
    """
    Example: Complete workflow from opt-in to message sending
    """
    print("=== Comprehensive Workflow Example ===")
    
    wa_account = env['whatsapp.account'].search([('active', '=', True)], limit=1)
    if not wa_account:
        return
    
    from odoo.addons.whatsapp.tools.whatsapp_api import WhatsAppApi
    from odoo.addons.whatsapp.tools.phone_validator import OptInManager, PhoneNumberValidator
    
    api = WhatsAppApi(wa_account)
    manager = OptInManager(env)
    
    # Customer phone number
    customer_phone = "+966 50 123 4567"
    
    try:
        # Step 1: Validate phone number
        is_valid, clean_phone, country = PhoneNumberValidator.validate_phone_format(customer_phone)
        if not is_valid:
            print(f"Invalid phone number: {customer_phone}")
            return
        
        print(f"Valid phone: {clean_phone} (Country: {country})")
        
        # Step 2: Record opt-in
        manager.record_opt_in(clean_phone, wa_account.id, 'website_form', True)
        print("Customer opted in successfully")
        
        # Step 3: Check session status
        session_status = api.get_session_status(clean_phone)
        print(f"Session status: {session_status}")
        
        # Step 4: Send welcome message
        if session_status['session_active']:
            # Send conversation message
            welcome_msg = "Welcome! Thank you for joining us. How can we help you today?"
        else:
            # Send template message
            welcome_msg = "welcome_template"
        
        result = api.send_message_smart(
            phone_number=clean_phone,
            message_type='text',
            content=welcome_msg,
            template_name='welcome_template' if not session_status['session_active'] else None,
            language_code='ar'
        )
        print(f"Welcome message sent: {result}")
        
        # Step 5: Get account statistics
        stats = api.get_account_statistics(7)
        print(f"Account statistics: {stats}")
        
        print("Workflow completed successfully!")
        
    except Exception as e:
        print(f"Error in workflow: {e}")

# Main execution function
def run_all_examples(env):
    """
    Run all examples
    """
    print("Starting Taqnyat WhatsApp Integration Examples")
    print("=" * 50)
    
    examples = [
        example_basic_message_sending,
        example_interactive_messages,
        example_media_handling,
        example_opt_in_management,
        example_template_management,
        example_webhook_simulation,
        example_comprehensive_workflow
    ]
    
    for example_func in examples:
        try:
            example_func(env)
            print()
        except Exception as e:
            print(f"Error in {example_func.__name__}: {e}")
            print()
    
    print("All examples completed!")

# Usage in Odoo shell:
# env = api.Environment(cr, uid, context)
# from odoo.addons.whatsapp.examples.taqnyat_usage_examples import run_all_examples
# run_all_examples(env)
